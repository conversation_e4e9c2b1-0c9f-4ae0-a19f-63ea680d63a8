# -*- coding: utf-8 -*-
"""
燕云十六声AI自动化工具 - 主入口文件
基于计算机视觉和AI技术的游戏自动化框架
"""

import sys
import os
import time
import threading
import traceback
from datetime import datetime

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox
    from PyQt5.QtCore import QTimer, pyqtSignal, QObject
    from PyQt5.QtGui import QIcon
    
    # 导入自定义模块
    from yanyunauto.utils.logger import logger
    from yanyunauto.utils.config_manager import ConfigManager
    from yanyunauto.core.game_controller import YanyunGameController
    from ui.main_window import MainWindow
    from yanyunauto.utils.state_manager import StateManager
    
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)

class YanyunAITool(QObject):
    """燕云十六声AI工具主类"""
    
    # 信号定义
    status_changed = pyqtSignal(str)
    log_message = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.app = None
        self.main_window = None
        self.game_controller = None
        self.config_manager = ConfigManager()
        self.state_manager = StateManager()
        self.running = False
        
        # 初始化日志
        logger.info("燕云十六声AI工具启动中...")
        
    def initialize(self):
        """初始化应用程序"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("燕云十六声AI工具")
            self.app.setApplicationVersion("1.0.0")
            
            # 设置应用图标
            if os.path.exists("datas/images/icon.ico"):
                self.app.setWindowIcon(QIcon("datas/images/icon.ico"))
            
            # 创建主窗口
            self.main_window = MainWindow(self)
            
            # 创建游戏控制器
            self.game_controller = YanyunGameController(self.state_manager)
            
            # 连接信号
            self.connect_signals()
            
            logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def connect_signals(self):
        """连接信号和槽"""
        if self.main_window:
            # 连接主窗口信号
            self.main_window.start_automation.connect(self.start_automation)
            self.main_window.stop_automation.connect(self.stop_automation)
            self.main_window.load_config.connect(self.load_config)
            self.main_window.save_config.connect(self.save_config)
            
        if self.game_controller:
            # 连接游戏控制器信号
            self.game_controller.status_changed.connect(self.on_status_changed)
            self.game_controller.log_message.connect(self.on_log_message)
    
    def start_automation(self, task_type="auto_battle"):
        """开始自动化任务"""
        if self.running:
            logger.warning("自动化任务已在运行中")
            return
            
        try:
            self.running = True
            self.state_manager.set_running(True)
            
            # 在新线程中启动游戏控制器
            self.automation_thread = threading.Thread(
                target=self.game_controller.start_task,
                args=(task_type,),
                daemon=True
            )
            self.automation_thread.start()
            
            logger.info(f"开始执行自动化任务: {task_type}")
            self.status_changed.emit(f"正在执行: {task_type}")
            
        except Exception as e:
            logger.error(f"启动自动化任务失败: {e}")
            self.running = False
            self.state_manager.set_running(False)
    
    def stop_automation(self):
        """停止自动化任务"""
        try:
            self.running = False
            self.state_manager.set_running(False)
            
            if self.game_controller:
                self.game_controller.stop_task()
            
            logger.info("自动化任务已停止")
            self.status_changed.emit("已停止")
            
        except Exception as e:
            logger.error(f"停止自动化任务失败: {e}")
    
    def load_config(self, config_path=None):
        """加载配置文件"""
        try:
            if config_path is None:
                config_path = "datas/configs/settings.ini"
            
            self.config_manager.load_config(config_path)
            logger.info(f"配置文件加载成功: {config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def save_config(self, config_path=None):
        """保存配置文件"""
        try:
            if config_path is None:
                config_path = "datas/configs/settings.ini"
            
            self.config_manager.save_config(config_path)
            logger.info(f"配置文件保存成功: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def on_status_changed(self, status):
        """状态变化处理"""
        self.status_changed.emit(status)
        if self.main_window:
            self.main_window.update_status(status)
    
    def on_log_message(self, message):
        """日志消息处理"""
        self.log_message.emit(message)
        if self.main_window:
            self.main_window.add_log_message(message)
    
    def run(self):
        """运行应用程序"""
        if not self.initialize():
            return 1
        
        # 显示主窗口
        self.main_window.show()
        
        # 加载默认配置
        self.load_config()
        
        # 运行应用程序事件循环
        return self.app.exec_()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.running:
                self.stop_automation()
            
            # 保存配置
            self.save_config()
            
            logger.info("应用程序清理完成")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

def main():
    """主函数"""
    try:
        # 创建应用实例
        yanyun_tool = YanyunAITool()
        
        # 设置异常处理
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            logger.error("未捕获的异常:", 
                        exc_info=(exc_type, exc_value, exc_traceback))
        
        sys.excepthook = handle_exception
        
        # 运行应用
        exit_code = yanyun_tool.run()
        
        # 清理资源
        yanyun_tool.cleanup()
        
        return exit_code
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        print(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
