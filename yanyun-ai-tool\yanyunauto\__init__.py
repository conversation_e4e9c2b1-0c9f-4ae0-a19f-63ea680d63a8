# -*- coding: utf-8 -*-
"""
燕云十六声自动化核心模块
YanyunAuto - 基于计算机视觉和AI的游戏自动化框架
"""

__version__ = "1.0.0"
__author__ = "YanyunAI Team"
__description__ = "燕云十六声AI自动化工具核心模块"

# 导入核心模块
from .utils.logger import logger
from .utils.config_manager import ConfigManager
from .utils.state_manager import StateManager

# 版本信息
VERSION_INFO = {
    "version": __version__,
    "author": __author__,
    "description": __description__
}

def get_version():
    """获取版本信息"""
    return __version__

def get_version_info():
    """获取详细版本信息"""
    return VERSION_INFO
