# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 状态管理模块
管理应用程序和游戏的各种状态
"""

import threading
import time
from typing import Dict, Any, Optional, Tuple
from enum import Enum
from .logger import logger

class GameState(Enum):
    """游戏状态枚举"""
    UNKNOWN = "unknown"
    MAIN_MENU = "main_menu"
    IN_GAME = "in_game"
    BATTLE = "battle"
    DIALOGUE = "dialogue"
    INVENTORY = "inventory"
    MAP = "map"
    QUEST = "quest"
    LOADING = "loading"

class TaskState(Enum):
    """任务状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class BattleState(Enum):
    """战斗状态枚举"""
    NONE = "none"
    SEARCHING = "searching"
    ENGAGING = "engaging"
    FIGHTING = "fighting"
    VICTORY = "victory"
    DEFEAT = "defeat"

class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self._lock = threading.RLock()
        
        # 应用程序状态
        self.app_running = False
        self.automation_running = False
        self.last_update_time = time.time()
        
        # 游戏状态
        self.game_state = GameState.UNKNOWN
        self.game_window_handle = None
        self.game_window_rect = (0, 0, 1920, 1080)
        self.game_fps = 0
        
        # 任务状态
        self.current_task = None
        self.task_state = TaskState.IDLE
        self.task_progress = 0.0
        self.task_start_time = None
        
        # 战斗状态
        self.battle_state = BattleState.NONE
        self.current_target = None
        self.player_hp = 100
        self.player_mp = 100
        self.battle_start_time = None
        
        # 位置和导航
        self.player_position = (0, 0)
        self.target_position = None
        self.current_path = []
        self.is_moving = False
        self.movement_speed = 1.0
        
        # 检测结果
        self.detected_objects = []
        self.last_detection_time = 0
        self.detection_image = None
        
        # 统计信息
        self.stats = {
            "battles_won": 0,
            "battles_lost": 0,
            "quests_completed": 0,
            "items_collected": 0,
            "total_runtime": 0,
            "errors_count": 0
        }
        
        # 错误和异常
        self.last_error = None
        self.error_count = 0
        self.consecutive_errors = 0
        
        logger.info("状态管理器初始化完成")
    
    def update_timestamp(self):
        """更新时间戳"""
        with self._lock:
            self.last_update_time = time.time()
    
    # 应用程序状态管理
    def set_running(self, running: bool):
        """设置运行状态"""
        with self._lock:
            self.app_running = running
            self.update_timestamp()
            logger.info(f"应用程序运行状态: {running}")
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        with self._lock:
            return self.app_running
    
    def set_automation_running(self, running: bool):
        """设置自动化运行状态"""
        with self._lock:
            self.automation_running = running
            self.update_timestamp()
            logger.info(f"自动化运行状态: {running}")
    
    def is_automation_running(self) -> bool:
        """检查自动化是否正在运行"""
        with self._lock:
            return self.automation_running
    
    # 游戏状态管理
    def set_game_state(self, state: GameState):
        """设置游戏状态"""
        with self._lock:
            if self.game_state != state:
                old_state = self.game_state
                self.game_state = state
                self.update_timestamp()
                logger.info(f"游戏状态变化: {old_state.value} -> {state.value}")
    
    def get_game_state(self) -> GameState:
        """获取游戏状态"""
        with self._lock:
            return self.game_state
    
    def set_game_window(self, handle: int, rect: Tuple[int, int, int, int]):
        """设置游戏窗口信息"""
        with self._lock:
            self.game_window_handle = handle
            self.game_window_rect = rect
            self.update_timestamp()
    
    def get_game_window(self) -> Tuple[Optional[int], Tuple[int, int, int, int]]:
        """获取游戏窗口信息"""
        with self._lock:
            return self.game_window_handle, self.game_window_rect
    
    # 任务状态管理
    def start_task(self, task_name: str):
        """开始任务"""
        with self._lock:
            self.current_task = task_name
            self.task_state = TaskState.RUNNING
            self.task_progress = 0.0
            self.task_start_time = time.time()
            self.update_timestamp()
            logger.log_task(task_name, "开始")
    
    def update_task_progress(self, progress: float):
        """更新任务进度"""
        with self._lock:
            self.task_progress = max(0.0, min(100.0, progress))
            self.update_timestamp()
    
    def complete_task(self, success: bool = True):
        """完成任务"""
        with self._lock:
            if self.current_task:
                if success:
                    self.task_state = TaskState.COMPLETED
                    self.stats["quests_completed"] += 1
                    logger.log_task(self.current_task, "完成")
                else:
                    self.task_state = TaskState.FAILED
                    logger.log_task(self.current_task, "失败")
                
                self.task_progress = 100.0 if success else 0.0
                self.update_timestamp()
    
    def cancel_task(self):
        """取消任务"""
        with self._lock:
            if self.current_task:
                self.task_state = TaskState.CANCELLED
                logger.log_task(self.current_task, "取消")
                self.update_timestamp()
    
    def get_task_info(self) -> Dict[str, Any]:
        """获取任务信息"""
        with self._lock:
            return {
                "name": self.current_task,
                "state": self.task_state,
                "progress": self.task_progress,
                "start_time": self.task_start_time,
                "duration": time.time() - self.task_start_time if self.task_start_time else 0
            }
    
    # 战斗状态管理
    def set_battle_state(self, state: BattleState):
        """设置战斗状态"""
        with self._lock:
            if self.battle_state != state:
                old_state = self.battle_state
                self.battle_state = state
                
                if state == BattleState.FIGHTING and old_state != BattleState.FIGHTING:
                    self.battle_start_time = time.time()
                elif state == BattleState.VICTORY:
                    self.stats["battles_won"] += 1
                elif state == BattleState.DEFEAT:
                    self.stats["battles_lost"] += 1
                
                self.update_timestamp()
                logger.log_battle(f"状态变化: {old_state.value} -> {state.value}")
    
    def set_player_stats(self, hp: int, mp: int):
        """设置玩家状态"""
        with self._lock:
            self.player_hp = hp
            self.player_mp = mp
            self.update_timestamp()
    
    def set_current_target(self, target: Optional[str]):
        """设置当前目标"""
        with self._lock:
            self.current_target = target
            self.update_timestamp()
    
    # 位置和导航管理
    def set_player_position(self, position: Tuple[float, float]):
        """设置玩家位置"""
        with self._lock:
            self.player_position = position
            self.update_timestamp()
    
    def set_target_position(self, position: Optional[Tuple[float, float]]):
        """设置目标位置"""
        with self._lock:
            self.target_position = position
            self.update_timestamp()
    
    def set_movement_state(self, is_moving: bool, speed: float = 1.0):
        """设置移动状态"""
        with self._lock:
            self.is_moving = is_moving
            self.movement_speed = speed
            self.update_timestamp()
    
    def set_current_path(self, path: list):
        """设置当前路径"""
        with self._lock:
            self.current_path = path.copy()
            self.update_timestamp()
    
    # 检测结果管理
    def update_detection_results(self, objects: list, image=None):
        """更新检测结果"""
        with self._lock:
            self.detected_objects = objects.copy()
            self.last_detection_time = time.time()
            if image is not None:
                self.detection_image = image
            self.update_timestamp()
    
    def get_detected_objects(self, object_type: Optional[str] = None) -> list:
        """获取检测到的对象"""
        with self._lock:
            if object_type is None:
                return self.detected_objects.copy()
            else:
                return [obj for obj in self.detected_objects if obj.get('label') == object_type]
    
    # 错误管理
    def record_error(self, error: str):
        """记录错误"""
        with self._lock:
            self.last_error = error
            self.error_count += 1
            self.consecutive_errors += 1
            self.stats["errors_count"] += 1
            self.update_timestamp()
            logger.error(f"记录错误: {error}")
    
    def clear_consecutive_errors(self):
        """清除连续错误计数"""
        with self._lock:
            self.consecutive_errors = 0
            self.update_timestamp()
    
    # 统计信息
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self.stats.copy()
            stats["current_task"] = self.current_task
            stats["game_state"] = self.game_state.value
            stats["battle_state"] = self.battle_state.value
            stats["uptime"] = time.time() - self.last_update_time
            return stats
    
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self.stats = {
                "battles_won": 0,
                "battles_lost": 0,
                "quests_completed": 0,
                "items_collected": 0,
                "total_runtime": 0,
                "errors_count": 0
            }
            self.update_timestamp()
            logger.info("统计信息已重置")
    
    def get_full_state(self) -> Dict[str, Any]:
        """获取完整状态信息"""
        with self._lock:
            return {
                "app_running": self.app_running,
                "automation_running": self.automation_running,
                "game_state": self.game_state.value,
                "task_info": self.get_task_info(),
                "battle_state": self.battle_state.value,
                "player_position": self.player_position,
                "target_position": self.target_position,
                "detected_objects_count": len(self.detected_objects),
                "stats": self.get_stats(),
                "last_update": self.last_update_time
            }
