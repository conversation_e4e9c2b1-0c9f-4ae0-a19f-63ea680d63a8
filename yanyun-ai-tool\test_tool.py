# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 测试脚本
用于验证工具的各个模块是否正常工作
"""

import sys
import os
import time
import traceback

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入...")
    print("=" * 50)
    
    try:
        # 测试核心模块
        print("导入核心模块...")
        from yanyunauto.utils.logger import logger
        from yanyunauto.utils.config_manager import ConfigManager
        from yanyunauto.utils.state_manager import StateManager
        print("✓ 核心模块导入成功")
        
        # 测试视觉模块
        print("导入视觉模块...")
        from yanyunauto.vision.screenshot import screen_capture
        from yanyunauto.vision.image_recognition import image_recognition
        print("✓ 视觉模块导入成功")
        
        # 测试检测模块
        print("导入检测模块...")
        from yanyunauto.detection.yolo_detector import YoloDetector
        print("✓ 检测模块导入成功")
        
        # 测试模拟模块
        print("导入模拟模块...")
        from yanyunauto.simulation.input_simulator import input_simulator
        print("✓ 模拟模块导入成功")
        
        # 测试寻路模块
        print("导入寻路模块...")
        from yanyunauto.pathfinding.astar_pathfinder import pathfinder
        print("✓ 寻路模块导入成功")
        
        # 测试游戏控制器
        print("导入游戏控制器...")
        from yanyunauto.core.game_controller import YanyunGameController
        print("✓ 游戏控制器导入成功")
        
        # 测试UI模块
        print("导入UI模块...")
        from ui.main_window import MainWindow
        print("✓ UI模块导入成功")
        
        print("\n✓ 所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 模块导入失败: {e}")
        print(traceback.format_exc())
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n" + "=" * 50)
    print("测试依赖包...")
    print("=" * 50)
    
    dependencies = [
        ("numpy", "数值计算"),
        ("cv2", "OpenCV图像处理"),
        ("PyQt5", "GUI框架"),
        ("pyautogui", "输入模拟"),
        ("win32gui", "Windows API"),
        ("onnxruntime", "ONNX推理引擎")
    ]
    
    failed_deps = []
    
    for dep_name, description in dependencies:
        try:
            if dep_name == "cv2":
                import cv2
            elif dep_name == "PyQt5":
                from PyQt5.QtWidgets import QApplication
            elif dep_name == "win32gui":
                import win32gui
            elif dep_name == "onnxruntime":
                import onnxruntime
            else:
                __import__(dep_name)
            
            print(f"✓ {dep_name} ({description}) - 可用")
            
        except ImportError as e:
            print(f"✗ {dep_name} ({description}) - 缺失: {e}")
            failed_deps.append(dep_name)
    
    if failed_deps:
        print(f"\n✗ 缺失依赖包: {', '.join(failed_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✓ 所有依赖包测试通过！")
        return True

def test_config():
    """测试配置管理"""
    print("\n" + "=" * 50)
    print("测试配置管理...")
    print("=" * 50)
    
    try:
        from yanyunauto.utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试配置读取
        confidence = config_manager.get("detection", "confidence_threshold", 0.6)
        print(f"✓ 读取配置 - 置信度阈值: {confidence}")
        
        # 测试配置设置
        config_manager.set("test", "value", "test_value")
        test_value = config_manager.get("test", "value")
        print(f"✓ 设置配置 - 测试值: {test_value}")
        
        print("\n✓ 配置管理测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 配置管理测试失败: {e}")
        print(traceback.format_exc())
        return False

def test_logger():
    """测试日志系统"""
    print("\n" + "=" * 50)
    print("测试日志系统...")
    print("=" * 50)
    
    try:
        from yanyunauto.utils.logger import logger
        
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.debug("这是一条调试日志")
        
        print("✓ 日志系统测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        print(traceback.format_exc())
        return False

def test_screenshot():
    """测试截图功能"""
    print("\n" + "=" * 50)
    print("测试截图功能...")
    print("=" * 50)
    
    try:
        from yanyunauto.vision.screenshot import screen_capture
        
        # 测试屏幕截图
        screenshot = screen_capture.capture_screen()
        if screenshot is not None:
            print(f"✓ 屏幕截图成功 - 尺寸: {screenshot.shape}")
        else:
            print("✗ 屏幕截图失败")
            return False
        
        # 测试窗口查找
        found = screen_capture.find_game_window()
        if found:
            print("✓ 找到游戏窗口")
        else:
            print("⚠ 未找到游戏窗口（这是正常的，如果游戏未运行）")
        
        print("\n✓ 截图功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 截图功能测试失败: {e}")
        print(traceback.format_exc())
        return False

def test_pathfinding():
    """测试寻路算法"""
    print("\n" + "=" * 50)
    print("测试寻路算法...")
    print("=" * 50)
    
    try:
        from yanyunauto.pathfinding.astar_pathfinder import AStarPathfinder
        
        pathfinder = AStarPathfinder(100, 100)  # 小地图测试
        
        # 测试路径查找
        start = (10, 10)
        goal = (90, 90)
        path = pathfinder.find_path(start, goal)
        
        if path:
            print(f"✓ 寻路成功 - 路径长度: {len(path)}")
        else:
            print("✗ 寻路失败")
            return False
        
        print("\n✓ 寻路算法测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 寻路算法测试失败: {e}")
        print(traceback.format_exc())
        return False

def test_state_manager():
    """测试状态管理"""
    print("\n" + "=" * 50)
    print("测试状态管理...")
    print("=" * 50)
    
    try:
        from yanyunauto.utils.state_manager import StateManager, GameState
        
        state_manager = StateManager()
        
        # 测试状态设置
        state_manager.set_game_state(GameState.IN_GAME)
        current_state = state_manager.get_game_state()
        print(f"✓ 状态设置成功 - 当前状态: {current_state.value}")
        
        # 测试任务管理
        state_manager.start_task("test_task")
        task_info = state_manager.get_task_info()
        print(f"✓ 任务管理成功 - 任务: {task_info['name']}")
        
        print("\n✓ 状态管理测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 状态管理测试失败: {e}")
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("燕云十六声AI工具 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖包", test_dependencies),
        ("配置管理", test_config),
        ("日志系统", test_logger),
        ("截图功能", test_screenshot),
        ("寻路算法", test_pathfinding),
        ("状态管理", test_state_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"\n✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！工具可以正常使用。")
        return 0
    else:
        print(f"\n⚠ 有 {total - passed} 个测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
