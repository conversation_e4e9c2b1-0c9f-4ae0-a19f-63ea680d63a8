# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 配置管理模块
管理应用程序的各种配置参数
"""

import os
import json
import configparser
from typing import Dict, Any, Optional
from .logger import logger

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir="datas/configs"):
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "settings.ini")
        self.game_config_file = os.path.join(config_dir, "game_settings.json")
        
        # 默认配置
        self.default_config = {
            # 游戏设置
            "game": {
                "window_title": "燕云十六声",
                "window_class": "UnityWndClass",
                "resolution_width": 1920,
                "resolution_height": 1080,
                "game_fps": 60
            },
            
            # 检测设置
            "detection": {
                "yolo_model_path": "datas/models/yanyun_yolov5.onnx",
                "confidence_threshold": 0.6,
                "nms_threshold": 0.4,
                "detection_interval": 0.1,
                "use_gpu": True
            },
            
            # 战斗设置
            "battle": {
                "auto_battle": True,
                "skill_cooldown": 1.0,
                "dodge_threshold": 0.3,
                "target_priority": ["boss", "elite", "normal"],
                "battle_timeout": 300
            },
            
            # 任务设置
            "task": {
                "auto_accept_quest": True,
                "auto_submit_quest": True,
                "quest_timeout": 600,
                "retry_count": 3
            },
            
            # 导航设置
            "navigation": {
                "pathfinding_algorithm": "astar",
                "movement_speed": 1.0,
                "rotation_speed": 2.0,
                "arrival_threshold": 5.0
            },
            
            # 界面设置
            "ui": {
                "show_detection_overlay": True,
                "show_fps": True,
                "log_level": "INFO",
                "auto_save_config": True
            }
        }
        
        self.config = self.default_config.copy()
        self._ensure_config_dir()
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            logger.info(f"创建配置目录: {self.config_dir}")
    
    def load_config(self, config_path: Optional[str] = None) -> bool:
        """加载配置文件"""
        try:
            if config_path is None:
                config_path = self.config_file
            
            if not os.path.exists(config_path):
                logger.warning(f"配置文件不存在，使用默认配置: {config_path}")
                self.save_config(config_path)
                return True
            
            # 根据文件扩展名选择加载方式
            if config_path.endswith('.json'):
                return self._load_json_config(config_path)
            else:
                return self._load_ini_config(config_path)
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def _load_json_config(self, config_path: str) -> bool:
        """加载JSON配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
            
            # 合并配置
            self._merge_config(loaded_config)
            logger.info(f"JSON配置文件加载成功: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"加载JSON配置文件失败: {e}")
            return False
    
    def _load_ini_config(self, config_path: str) -> bool:
        """加载INI配置文件"""
        try:
            config_parser = configparser.ConfigParser()
            config_parser.read(config_path, encoding='utf-8')
            
            # 转换为字典格式
            loaded_config = {}
            for section in config_parser.sections():
                loaded_config[section] = {}
                for key, value in config_parser.items(section):
                    # 尝试转换数据类型
                    loaded_config[section][key] = self._convert_value(value)
            
            # 合并配置
            self._merge_config(loaded_config)
            logger.info(f"INI配置文件加载成功: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"加载INI配置文件失败: {e}")
            return False
    
    def save_config(self, config_path: Optional[str] = None) -> bool:
        """保存配置文件"""
        try:
            if config_path is None:
                config_path = self.config_file
            
            # 根据文件扩展名选择保存方式
            if config_path.endswith('.json'):
                return self._save_json_config(config_path)
            else:
                return self._save_ini_config(config_path)
                
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _save_json_config(self, config_path: str) -> bool:
        """保存JSON配置文件"""
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            logger.info(f"JSON配置文件保存成功: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存JSON配置文件失败: {e}")
            return False
    
    def _save_ini_config(self, config_path: str) -> bool:
        """保存INI配置文件"""
        try:
            config_parser = configparser.ConfigParser()
            
            # 转换为INI格式
            for section_name, section_data in self.config.items():
                config_parser.add_section(section_name)
                for key, value in section_data.items():
                    config_parser.set(section_name, key, str(value))
            
            with open(config_path, 'w', encoding='utf-8') as f:
                config_parser.write(f)
            
            logger.info(f"INI配置文件保存成功: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存INI配置文件失败: {e}")
            return False
    
    def _merge_config(self, loaded_config: Dict[str, Any]):
        """合并配置"""
        for section, values in loaded_config.items():
            if section in self.config:
                self.config[section].update(values)
            else:
                self.config[section] = values
    
    def _convert_value(self, value: str) -> Any:
        """转换配置值的数据类型"""
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 整数
        try:
            return int(value)
        except ValueError:
            pass
        
        # 浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # 列表（逗号分隔）
        if ',' in value:
            return [item.strip() for item in value.split(',')]
        
        # 字符串
        return value
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            return self.config.get(section, {}).get(key, default)
        except Exception:
            return default
    
    def set(self, section: str, key: str, value: Any):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个配置段"""
        return self.config.get(section, {})
    
    def set_section(self, section: str, values: Dict[str, Any]):
        """设置整个配置段"""
        self.config[section] = values
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        logger.info("配置已重置为默认值")
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config.copy()
