# -*- coding: utf-8 -*-
"""
燕云十六声AI自动化工具 - 安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# 读取requirements文件
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="yanyun-ai-tool",
    version="1.0.0",
    author="YanyunAI Team",
    author_email="<EMAIL>",
    description="燕云十六声AI自动化工具 - 基于计算机视觉和AI技术的游戏自动化框架",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/yanyun-ai-tool",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Games/Entertainment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "gpu": ["onnxruntime-gpu>=1.12.0"],
        "dev": ["pytest>=7.0.0", "black>=22.0.0", "flake8>=4.0.0"],
        "performance": ["numba>=0.56.0", "cython>=0.29.0"],
    },
    entry_points={
        "console_scripts": [
            "yanyun-ai=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.ini", "*.json", "*.png", "*.jpg", "*.onnx"],
    },
    zip_safe=False,
    keywords="ai automation game yanyun computer-vision yolo",
    project_urls={
        "Bug Reports": "https://github.com/your-repo/yanyun-ai-tool/issues",
        "Source": "https://github.com/your-repo/yanyun-ai-tool",
        "Documentation": "https://github.com/your-repo/yanyun-ai-tool/wiki",
    },
)
