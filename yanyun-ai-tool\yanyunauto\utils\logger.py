# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 日志模块
提供统一的日志记录功能
"""

import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler
import coloredlogs

class YanyunLogger:
    """燕云十六声专用日志器"""
    
    def __init__(self, name="YanyunAI", log_dir="logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 创建日志目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # 创建logger
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if self.logger.handlers:
            return
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 普通日志
        log_file = os.path.join(self.log_dir, f"{self.name}.log")
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 文件处理器 - 错误日志
        error_log_file = os.path.join(self.log_dir, f"{self.name}_error.log")
        error_handler = RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.logger.addHandler(error_handler)
        
        # 设置彩色日志
        try:
            coloredlogs.install(
                level='INFO',
                logger=self.logger,
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        except:
            pass  # 如果coloredlogs不可用，忽略错误
    
    def debug(self, message):
        """调试日志"""
        self.logger.debug(message)
    
    def info(self, message):
        """信息日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """警告日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """错误日志"""
        self.logger.error(message)
    
    def critical(self, message):
        """严重错误日志"""
        self.logger.critical(message)
    
    def log_game_action(self, action, details=""):
        """记录游戏操作"""
        self.info(f"[游戏操作] {action} {details}")
    
    def log_detection(self, target, confidence=None, position=None):
        """记录检测结果"""
        msg = f"[检测] 发现目标: {target}"
        if confidence:
            msg += f" 置信度: {confidence:.2f}"
        if position:
            msg += f" 位置: {position}"
        self.info(msg)
    
    def log_battle(self, action, target=None, result=None):
        """记录战斗日志"""
        msg = f"[战斗] {action}"
        if target:
            msg += f" 目标: {target}"
        if result:
            msg += f" 结果: {result}"
        self.info(msg)
    
    def log_task(self, task_name, status, details=""):
        """记录任务日志"""
        self.info(f"[任务] {task_name} - {status} {details}")
    
    def log_navigation(self, action, from_pos=None, to_pos=None):
        """记录导航日志"""
        msg = f"[导航] {action}"
        if from_pos and to_pos:
            msg += f" 从 {from_pos} 到 {to_pos}"
        self.info(msg)

# 创建全局日志器实例
logger = YanyunLogger()

# 提供便捷的日志函数
def debug(message):
    logger.debug(message)

def info(message):
    logger.info(message)

def warning(message):
    logger.warning(message)

def error(message):
    logger.error(message)

def critical(message):
    logger.critical(message)

def log_game_action(action, details=""):
    logger.log_game_action(action, details)

def log_detection(target, confidence=None, position=None):
    logger.log_detection(target, confidence, position)

def log_battle(action, target=None, result=None):
    logger.log_battle(action, target, result)

def log_task(task_name, status, details=""):
    logger.log_task(task_name, status, details)

def log_navigation(action, from_pos=None, to_pos=None):
    logger.log_navigation(action, from_pos, to_pos)
