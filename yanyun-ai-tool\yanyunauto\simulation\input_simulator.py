# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 输入模拟模块
提供键盘鼠标模拟操作功能
"""

import time
import random
import pyautogui
import win32api
import win32con
import win32gui
from ctypes import windll, c_long, c_ulong, Structure, Union, c_int, POINTER, sizeof
from ctypes.wintypes import DWORD, WORD, LONG, HWND, POINT
from typing import Tuple, Optional, List
from ..utils.logger import logger

# 禁用pyautogui的安全特性
pyautogui.PAUSE = 0.01
pyautogui.FAILSAFE = False

class InputSimulator:
    """输入模拟器"""
    
    def __init__(self, window_handle: Optional[int] = None):
        self.window_handle = window_handle
        self.last_mouse_pos = (0, 0)
        self.key_states = {}  # 记录按键状态
        
        # 燕云十六声默认按键映射
        self.key_mapping = {
            # 移动
            "move_forward": "w",
            "move_backward": "s", 
            "move_left": "a",
            "move_right": "d",
            "jump": "space",
            "dodge": "shift",
            "run": "shift",
            
            # 战斗
            "attack": "left_click",
            "heavy_attack": "right_click",
            "skill_1": "1",
            "skill_2": "2", 
            "skill_3": "3",
            "skill_4": "4",
            "ultimate": "r",
            "block": "right_click",
            
            # 交互
            "interact": "f",
            "pickup": "f",
            "confirm": "enter",
            "cancel": "esc",
            "menu": "tab",
            "inventory": "i",
            "map": "m",
            
            # 其他
            "chat": "enter",
            "screenshot": "f12"
        }
        
        logger.info("输入模拟器初始化完成")
    
    def set_window_handle(self, handle: int):
        """设置目标窗口句柄"""
        self.window_handle = handle
        logger.info(f"设置目标窗口句柄: {handle}")
    
    def activate_window(self) -> bool:
        """激活目标窗口"""
        if not self.window_handle:
            return False
        
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(self.window_handle):
                win32gui.ShowWindow(self.window_handle, win32con.SW_RESTORE)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.1)
            return True
            
        except Exception as e:
            logger.error(f"激活窗口失败: {e}")
            return False
    
    # 鼠标操作
    def move_mouse(self, x: int, y: int, duration: float = 0.1):
        """移动鼠标到指定位置"""
        try:
            if duration > 0:
                pyautogui.moveTo(x, y, duration=duration)
            else:
                pyautogui.moveTo(x, y)
            
            self.last_mouse_pos = (x, y)
            logger.debug(f"鼠标移动到: ({x}, {y})")
            
        except Exception as e:
            logger.error(f"移动鼠标失败: {e}")
    
    def move_mouse_relative(self, dx: int, dy: int, duration: float = 0.1):
        """相对移动鼠标"""
        try:
            current_x, current_y = pyautogui.position()
            new_x = current_x + dx
            new_y = current_y + dy
            self.move_mouse(new_x, new_y, duration)
            
        except Exception as e:
            logger.error(f"相对移动鼠标失败: {e}")
    
    def click_mouse(self, 
                   button: str = "left", 
                   x: Optional[int] = None, 
                   y: Optional[int] = None,
                   clicks: int = 1,
                   interval: float = 0.1):
        """点击鼠标"""
        try:
            if x is not None and y is not None:
                self.move_mouse(x, y, 0.05)
                time.sleep(0.05)
            
            if button == "left":
                pyautogui.click(clicks=clicks, interval=interval, button='left')
            elif button == "right":
                pyautogui.click(clicks=clicks, interval=interval, button='right')
            elif button == "middle":
                pyautogui.click(clicks=clicks, interval=interval, button='middle')
            
            logger.log_game_action(f"鼠标{button}键点击", f"位置: ({x}, {y})" if x and y else "")
            
        except Exception as e:
            logger.error(f"鼠标点击失败: {e}")
    
    def mouse_down(self, button: str = "left", x: Optional[int] = None, y: Optional[int] = None):
        """按下鼠标按键"""
        try:
            if x is not None and y is not None:
                self.move_mouse(x, y, 0.05)
                time.sleep(0.05)
            
            pyautogui.mouseDown(button=button)
            logger.debug(f"鼠标{button}键按下")
            
        except Exception as e:
            logger.error(f"鼠标按下失败: {e}")
    
    def mouse_up(self, button: str = "left"):
        """释放鼠标按键"""
        try:
            pyautogui.mouseUp(button=button)
            logger.debug(f"鼠标{button}键释放")
            
        except Exception as e:
            logger.error(f"鼠标释放失败: {e}")
    
    def drag_mouse(self, 
                  start_x: int, start_y: int, 
                  end_x: int, end_y: int, 
                  duration: float = 0.5,
                  button: str = "left"):
        """拖拽鼠标"""
        try:
            self.move_mouse(start_x, start_y, 0.1)
            time.sleep(0.1)
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button=button)
            logger.log_game_action("鼠标拖拽", f"从({start_x}, {start_y})到({end_x}, {end_y})")
            
        except Exception as e:
            logger.error(f"鼠标拖拽失败: {e}")
    
    def scroll_mouse(self, clicks: int, x: Optional[int] = None, y: Optional[int] = None):
        """滚动鼠标滚轮"""
        try:
            if x is not None and y is not None:
                self.move_mouse(x, y, 0.05)
                time.sleep(0.05)
            
            pyautogui.scroll(clicks)
            logger.debug(f"鼠标滚轮滚动: {clicks}")
            
        except Exception as e:
            logger.error(f"鼠标滚动失败: {e}")
    
    # 键盘操作
    def press_key(self, key: str, duration: float = 0.1):
        """按下并释放按键"""
        try:
            pyautogui.press(key)
            time.sleep(duration)
            logger.log_game_action(f"按键按下", key)
            
        except Exception as e:
            logger.error(f"按键操作失败: {e}")
    
    def key_down(self, key: str):
        """按下按键（不释放）"""
        try:
            pyautogui.keyDown(key)
            self.key_states[key] = True
            logger.debug(f"按键按下: {key}")
            
        except Exception as e:
            logger.error(f"按键按下失败: {e}")
    
    def key_up(self, key: str):
        """释放按键"""
        try:
            pyautogui.keyUp(key)
            self.key_states[key] = False
            logger.debug(f"按键释放: {key}")
            
        except Exception as e:
            logger.error(f"按键释放失败: {e}")
    
    def hold_key(self, key: str, duration: float):
        """按住按键指定时间"""
        try:
            self.key_down(key)
            time.sleep(duration)
            self.key_up(key)
            logger.log_game_action(f"按住按键", f"{key} {duration}秒")
            
        except Exception as e:
            logger.error(f"按住按键失败: {e}")
    
    def type_text(self, text: str, interval: float = 0.05):
        """输入文本"""
        try:
            pyautogui.typewrite(text, interval=interval)
            logger.log_game_action("输入文本", text)
            
        except Exception as e:
            logger.error(f"输入文本失败: {e}")
    
    def key_combination(self, keys: List[str], duration: float = 0.1):
        """按键组合"""
        try:
            # 按下所有按键
            for key in keys:
                self.key_down(key)
                time.sleep(0.02)
            
            time.sleep(duration)
            
            # 释放所有按键（逆序）
            for key in reversed(keys):
                self.key_up(key)
                time.sleep(0.02)
            
            logger.log_game_action("按键组合", "+".join(keys))
            
        except Exception as e:
            logger.error(f"按键组合失败: {e}")
    
    # 游戏特定操作
    def game_action(self, action: str, **kwargs):
        """执行游戏动作"""
        if action not in self.key_mapping:
            logger.warning(f"未知的游戏动作: {action}")
            return
        
        key = self.key_mapping[action]
        
        if key == "left_click":
            self.click_mouse("left", **kwargs)
        elif key == "right_click":
            self.click_mouse("right", **kwargs)
        else:
            duration = kwargs.get("duration", 0.1)
            self.press_key(key, duration)
        
        logger.log_game_action(f"游戏动作: {action}", f"按键: {key}")
    
    def move_character(self, direction: str, duration: float = 1.0):
        """移动角色"""
        direction_keys = {
            "forward": "w",
            "backward": "s",
            "left": "a", 
            "right": "d",
            "up": "w",
            "down": "s"
        }
        
        if direction in direction_keys:
            key = direction_keys[direction]
            self.hold_key(key, duration)
            logger.log_game_action(f"角色移动", f"方向: {direction}, 时长: {duration}秒")
    
    def attack_sequence(self, sequence: List[str], intervals: List[float] = None):
        """执行攻击序列"""
        if intervals is None:
            intervals = [0.5] * len(sequence)
        
        for i, action in enumerate(sequence):
            self.game_action(action)
            if i < len(intervals):
                time.sleep(intervals[i])
        
        logger.log_game_action("攻击序列", f"动作: {sequence}")
    
    def dodge_roll(self, direction: str = "backward"):
        """闪避翻滚"""
        # 先按方向键，再按闪避键
        if direction in ["forward", "backward", "left", "right"]:
            direction_key = self.key_mapping[f"move_{direction}"]
            dodge_key = self.key_mapping["dodge"]
            
            self.key_down(direction_key)
            time.sleep(0.1)
            self.press_key(dodge_key, 0.2)
            self.key_up(direction_key)
            
            logger.log_battle("闪避翻滚", direction)
    
    def interact_with_object(self, x: int, y: int):
        """与对象交互"""
        self.move_mouse(x, y, 0.2)
        time.sleep(0.2)
        self.game_action("interact")
        logger.log_game_action("对象交互", f"位置: ({x}, {y})")
    
    def pickup_item(self, x: int, y: int):
        """拾取物品"""
        self.move_mouse(x, y, 0.2)
        time.sleep(0.2)
        self.game_action("pickup")
        logger.log_game_action("拾取物品", f"位置: ({x}, {y})")
    
    def open_menu(self, menu_type: str = "main"):
        """打开菜单"""
        menu_keys = {
            "main": "tab",
            "inventory": "i",
            "map": "m",
            "skills": "k",
            "quests": "j"
        }
        
        if menu_type in menu_keys:
            self.press_key(menu_keys[menu_type])
            logger.log_game_action(f"打开{menu_type}菜单")
    
    def close_menu(self):
        """关闭菜单"""
        self.press_key("esc")
        logger.log_game_action("关闭菜单")
    
    # 工具方法
    def add_random_delay(self, base_delay: float = 0.1, variance: float = 0.05):
        """添加随机延迟（模拟人类操作）"""
        delay = base_delay + random.uniform(-variance, variance)
        time.sleep(max(0.01, delay))
    
    def is_key_pressed(self, key: str) -> bool:
        """检查按键是否被按下"""
        return self.key_states.get(key, False)
    
    def release_all_keys(self):
        """释放所有按下的按键"""
        for key, is_pressed in self.key_states.items():
            if is_pressed:
                self.key_up(key)
        logger.info("释放所有按键")
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """获取当前鼠标位置"""
        return pyautogui.position()

# 全局输入模拟器实例
input_simulator = InputSimulator()
