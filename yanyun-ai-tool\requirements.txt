# 燕云十六声AI自动化工具依赖包

# 核心依赖
numpy>=1.21.0
opencv-python>=4.5.0
Pillow>=8.3.0

# 深度学习和AI
onnxruntime-gpu>=1.12.0
onnxruntime>=1.12.0

# GUI框架
PyQt5>=5.15.0
qt-material>=2.14

# 输入模拟
pyautogui>=0.9.54
pynput>=1.7.6
pywin32>=306

# 图像处理
scikit-image>=0.18.0
matplotlib>=3.5.0

# 日志和配置
coloredlogs>=15.0
configparser>=5.2.0

# 网络和数据
requests>=2.28.0
websockets>=10.0

# 工具库
tqdm>=4.64.0
psutil>=5.9.0

# 开发工具
pytest>=7.0.0
black>=22.0.0
flake8>=4.0.0

# 可选依赖（GPU加速）
# cupy-cuda11x>=10.0.0  # CUDA 11.x
# cupy-cuda12x>=12.0.0  # CUDA 12.x

# 可选依赖（性能优化）
numba>=0.56.0
cython>=0.29.0
