# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 游戏控制器
统一管理游戏自动化功能的核心控制器
"""

import time
import threading
from typing import Optional, Dict, Any, List
from PyQt5.QtCore import QObject, pyqtSignal

from ..utils.logger import logger
from ..utils.state_manager import StateManager, GameState, TaskState, BattleState
from ..vision.screenshot import screen_capture
from ..vision.image_recognition import image_recognition
from ..detection.yolo_detector import get_yolo_detector
from ..simulation.input_simulator import input_simulator
from ..pathfinding.astar_pathfinder import pathfinder
# from .battle_system import BattleSystem
# from .quest_system import QuestSystem
# from .collection_system import CollectionSystem

class YanyunGameController(QObject):
    """燕云十六声游戏控制器"""
    
    # 信号定义
    status_changed = pyqtSignal(str)
    log_message = pyqtSignal(str)
    detection_updated = pyqtSignal(list)
    
    def __init__(self, state_manager: StateManager):
        super().__init__()
        self.state_manager = state_manager
        self.running = False
        self.current_task = None
        self.task_thread = None
        
        # 初始化子系统
        # self.battle_system = BattleSystem(self.state_manager, input_simulator)
        # self.quest_system = QuestSystem(self.state_manager, input_simulator)
        # self.collection_system = CollectionSystem(self.state_manager, input_simulator)
        
        # 获取检测器
        self.yolo_detector = get_yolo_detector()
        
        # 连接信号
        self._connect_signals()
        
        logger.info("游戏控制器初始化完成")
    
    def _connect_signals(self):
        """连接子系统信号"""
        # 战斗系统信号
        # self.battle_system.battle_started.connect(self._on_battle_started)
        # self.battle_system.battle_ended.connect(self._on_battle_ended)
        # self.battle_system.target_changed.connect(self._on_target_changed)

        # 任务系统信号
        # self.quest_system.quest_started.connect(self._on_quest_started)
        # self.quest_system.quest_completed.connect(self._on_quest_completed)
        # self.quest_system.quest_failed.connect(self._on_quest_failed)

        # 采集系统信号
        # self.collection_system.item_collected.connect(self._on_item_collected)
        pass
    
    def start_task(self, task_type: str, **kwargs):
        """开始执行任务"""
        if self.running:
            logger.warning("任务已在运行中")
            return
        
        self.running = True
        self.current_task = task_type
        self.state_manager.start_task(task_type)
        
        # 在新线程中执行任务
        self.task_thread = threading.Thread(
            target=self._execute_task,
            args=(task_type,),
            kwargs=kwargs,
            daemon=True
        )
        self.task_thread.start()
        
        logger.info(f"开始执行任务: {task_type}")
        self.status_changed.emit(f"正在执行: {task_type}")
    
    def stop_task(self):
        """停止当前任务"""
        self.running = False
        
        # 停止所有子系统
        # self.battle_system.stop()
        # self.quest_system.stop()
        # self.collection_system.stop()
        
        # 释放所有按键
        input_simulator.release_all_keys()
        
        if self.current_task:
            self.state_manager.cancel_task()
            logger.info(f"任务已停止: {self.current_task}")
            self.current_task = None
        
        self.status_changed.emit("已停止")
    
    def _execute_task(self, task_type: str, **kwargs):
        """执行具体任务"""
        try:
            # 初始化游戏环境
            if not self._initialize_game_environment():
                self.state_manager.complete_task(False)
                return
            
            # 根据任务类型执行相应逻辑
            if task_type == "auto_battle":
                self._execute_auto_battle(**kwargs)
            elif task_type == "auto_quest":
                self._execute_auto_quest(**kwargs)
            elif task_type == "auto_collection":
                self._execute_auto_collection(**kwargs)
            elif task_type == "auto_exploration":
                self._execute_auto_exploration(**kwargs)
            elif task_type == "custom_script":
                self._execute_custom_script(**kwargs)
            else:
                logger.error(f"未知的任务类型: {task_type}")
                self.state_manager.complete_task(False)
                return
            
            # 任务完成
            if self.running:
                self.state_manager.complete_task(True)
                logger.info(f"任务完成: {task_type}")
            
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            self.state_manager.complete_task(False)
        finally:
            self.running = False
    
    def _initialize_game_environment(self) -> bool:
        """初始化游戏环境"""
        try:
            # 查找游戏窗口
            if not screen_capture.find_game_window():
                logger.error("未找到游戏窗口")
                return False
            
            # 激活游戏窗口
            if not screen_capture.activate_window():
                logger.warning("无法激活游戏窗口")
            
            # 设置输入模拟器的目标窗口
            input_simulator.set_window_handle(screen_capture.window_handle)
            
            # 更新状态管理器
            window_handle, window_rect = screen_capture.window_handle, screen_capture.window_rect
            self.state_manager.set_game_window(window_handle, window_rect)
            
            # 检测当前游戏状态
            self._detect_game_state()
            
            logger.info("游戏环境初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化游戏环境失败: {e}")
            return False
    
    def _detect_game_state(self):
        """检测当前游戏状态"""
        try:
            # 截取游戏画面
            screenshot = screen_capture.capture_window()
            if screenshot is None:
                return
            
            # 检测UI元素
            ui_elements = image_recognition.detect_ui_elements(screenshot)
            
            # 根据UI元素判断游戏状态
            if ui_elements.get("health_bar"):
                self.state_manager.set_game_state(GameState.IN_GAME)
            elif any(ui_elements.get("menus", [])):
                self.state_manager.set_game_state(GameState.MAIN_MENU)
            elif any(ui_elements.get("dialogs", [])):
                self.state_manager.set_game_state(GameState.DIALOGUE)
            else:
                self.state_manager.set_game_state(GameState.UNKNOWN)
            
        except Exception as e:
            logger.error(f"检测游戏状态失败: {e}")
    
    def _execute_auto_battle(self, **kwargs):
        """执行自动战斗"""
        logger.info("开始自动战斗模式")
        
        # 配置战斗参数
        battle_config = {
            "target_priority": kwargs.get("target_priority", ["boss", "elite", "normal"]),
            "use_skills": kwargs.get("use_skills", True),
            "auto_dodge": kwargs.get("auto_dodge", True),
            "battle_timeout": kwargs.get("battle_timeout", 300)
        }
        
        # self.battle_system.configure(battle_config)

        while self.running:
            try:
                # 截取游戏画面
                screenshot = screen_capture.capture_window()
                if screenshot is None:
                    time.sleep(0.1)
                    continue

                # YOLO目标检测
                detections, result_image = self.yolo_detector.detect(screenshot, draw_results=True)
                self.state_manager.update_detection_results(detections, result_image)
                self.detection_updated.emit(detections)

                # 分析战斗场景
                battle_info = image_recognition.analyze_battle_scene(screenshot)

                if battle_info["in_battle"]:
                    # 进入战斗模式
                    self.state_manager.set_battle_state(BattleState.FIGHTING)
                    # self.battle_system.execute_battle(detections, battle_info)
                    self._simple_battle_logic(detections, battle_info)
                else:
                    # 搜索敌人
                    self.state_manager.set_battle_state(BattleState.SEARCHING)
                    self._search_for_enemies(detections)

                time.sleep(0.1)  # 控制循环频率

            except Exception as e:
                logger.error(f"自动战斗执行错误: {e}")
                time.sleep(1)
    
    def _execute_auto_quest(self, **kwargs):
        """执行自动任务"""
        logger.info("开始自动任务模式")
        
        quest_config = {
            "auto_accept": kwargs.get("auto_accept", True),
            "auto_submit": kwargs.get("auto_submit", True),
            "quest_timeout": kwargs.get("quest_timeout", 600)
        }
        
        # self.quest_system.configure(quest_config)

        while self.running:
            try:
                screenshot = screen_capture.capture_window()
                if screenshot is None:
                    time.sleep(0.1)
                    continue

                detections, _ = self.yolo_detector.detect(screenshot)
                self.state_manager.update_detection_results(detections)

                # 执行任务逻辑
                # self.quest_system.execute_quest_logic(detections)
                self._simple_quest_logic(detections)

                time.sleep(0.5)

            except Exception as e:
                logger.error(f"自动任务执行错误: {e}")
                time.sleep(1)
    
    def _execute_auto_collection(self, **kwargs):
        """执行自动采集"""
        logger.info("开始自动采集模式")
        
        collection_config = {
            "target_items": kwargs.get("target_items", ["采集物", "宝箱", "装备"]),
            "collection_radius": kwargs.get("collection_radius", 100),
            "auto_move": kwargs.get("auto_move", True)
        }
        
        # self.collection_system.configure(collection_config)

        while self.running:
            try:
                screenshot = screen_capture.capture_window()
                if screenshot is None:
                    time.sleep(0.1)
                    continue

                detections, _ = self.yolo_detector.detect(screenshot)
                self.state_manager.update_detection_results(detections)

                # 执行采集逻辑
                # self.collection_system.execute_collection(detections)
                self._simple_collection_logic(detections, collection_config)

                time.sleep(0.3)

            except Exception as e:
                logger.error(f"自动采集执行错误: {e}")
                time.sleep(1)
    
    def _execute_auto_exploration(self, **kwargs):
        """执行自动探索"""
        logger.info("开始自动探索模式")
        
        # 结合战斗、任务和采集
        while self.running:
            try:
                screenshot = screen_capture.capture_window()
                if screenshot is None:
                    time.sleep(0.1)
                    continue
                
                detections, _ = self.yolo_detector.detect(screenshot)
                self.state_manager.update_detection_results(detections)
                
                # 优先级：战斗 > 任务 > 采集 > 移动
                enemies = self.yolo_detector.filter_detections(detections, ["敌人", "BOSS", "精英怪"])
                if enemies:
                    # self.battle_system.execute_battle(detections, {})
                    self._simple_battle_logic(detections, {})
                else:
                    # 检查任务目标
                    quest_targets = self.yolo_detector.filter_detections(detections, ["任务目标", "NPC"])
                    if quest_targets:
                        # self.quest_system.execute_quest_logic(detections)
                        self._simple_quest_logic(detections)
                    else:
                        # 采集物品
                        collectibles = self.yolo_detector.filter_detections(detections, ["采集物", "宝箱"])
                        if collectibles:
                            # self.collection_system.execute_collection(detections)
                            self._simple_collection_logic(detections, {"target_items": ["采集物", "宝箱"]})
                        else:
                            # 随机移动探索
                            self._random_exploration()
                
                time.sleep(0.2)
                
            except Exception as e:
                logger.error(f"自动探索执行错误: {e}")
                time.sleep(1)
    
    def _execute_custom_script(self, **kwargs):
        """执行自定义脚本"""
        script_path = kwargs.get("script_path")
        if not script_path:
            logger.error("未指定脚本路径")
            return
        
        logger.info(f"执行自定义脚本: {script_path}")
        # TODO: 实现脚本执行逻辑
    
    def _search_for_enemies(self, detections: List[Dict[str, Any]]):
        """搜索敌人"""
        enemies = self.yolo_detector.filter_detections(detections, ["敌人", "BOSS", "精英怪", "普通怪"])
        
        if enemies:
            # 找到最近的敌人
            screen_center = (960, 540)  # 假设1920x1080分辨率
            closest_enemy = self.yolo_detector.get_closest_detection(enemies, screen_center)
            
            if closest_enemy:
                # 移动到敌人位置
                target_pos = closest_enemy["center"]
                input_simulator.move_mouse(target_pos[0], target_pos[1], 0.2)
                input_simulator.click_mouse("left")
                
                logger.log_battle("发现敌人", closest_enemy["label"], f"位置: {target_pos}")
        else:
            # 没有发现敌人，随机移动
            self._random_movement()
    
    def _random_movement(self):
        """随机移动"""
        import random
        
        directions = ["forward", "backward", "left", "right"]
        direction = random.choice(directions)
        duration = random.uniform(1.0, 3.0)
        
        input_simulator.move_character(direction, duration)
        logger.debug(f"随机移动: {direction} {duration:.1f}秒")
    
    def _random_exploration(self):
        """随机探索"""
        import random
        
        # 随机选择探索动作
        actions = ["move", "look_around", "jump"]
        action = random.choice(actions)
        
        if action == "move":
            self._random_movement()
        elif action == "look_around":
            # 转动视角
            dx = random.randint(-200, 200)
            dy = random.randint(-100, 100)
            input_simulator.move_mouse_relative(dx, dy, 0.5)
        elif action == "jump":
            input_simulator.game_action("jump")
        
        time.sleep(random.uniform(0.5, 2.0))

    def _simple_battle_logic(self, detections, battle_info):
        """简化的战斗逻辑"""
        enemies = self.yolo_detector.filter_detections(detections, ["敌人", "BOSS", "精英怪", "普通怪"])

        if enemies:
            # 选择最近的敌人
            screen_center = (960, 540)
            closest_enemy = self.yolo_detector.get_closest_detection(enemies, screen_center)

            if closest_enemy:
                # 移动鼠标到敌人位置并攻击
                target_pos = closest_enemy["center"]
                input_simulator.move_mouse(target_pos[0], target_pos[1], 0.1)
                input_simulator.game_action("attack")

                # 随机使用技能
                import random
                if random.random() < 0.3:  # 30%概率使用技能
                    skill = random.choice(["skill_1", "skill_2", "skill_3"])
                    input_simulator.game_action(skill)

                logger.log_battle("攻击敌人", closest_enemy["label"])

    def _simple_quest_logic(self, detections):
        """简化的任务逻辑"""
        npcs = self.yolo_detector.filter_detections(detections, ["NPC", "任务目标"])

        if npcs:
            # 与最近的NPC交互
            screen_center = (960, 540)
            closest_npc = self.yolo_detector.get_closest_detection(npcs, screen_center)

            if closest_npc:
                npc_pos = closest_npc["center"]
                input_simulator.interact_with_object(npc_pos[0], npc_pos[1])
                logger.log_game_action("与NPC交互", closest_npc["label"])

    def _simple_collection_logic(self, detections, config):
        """简化的采集逻辑"""
        target_items = config.get("target_items", ["采集物", "宝箱", "装备"])
        collectibles = self.yolo_detector.filter_detections(detections, target_items)

        if collectibles:
            # 采集最近的物品
            screen_center = (960, 540)
            closest_item = self.yolo_detector.get_closest_detection(collectibles, screen_center)

            if closest_item:
                item_pos = closest_item["center"]
                input_simulator.pickup_item(item_pos[0], item_pos[1])
                logger.log_game_action("采集物品", closest_item["label"])
                self.state_manager.stats["items_collected"] += 1

    # 信号处理方法
    def _on_battle_started(self, enemy_info):
        """战斗开始处理"""
        self.status_changed.emit(f"战斗开始: {enemy_info.get('label', '未知敌人')}")
    
    def _on_battle_ended(self, result):
        """战斗结束处理"""
        self.status_changed.emit(f"战斗结束: {result}")
    
    def _on_target_changed(self, target_info):
        """目标变更处理"""
        self.state_manager.set_current_target(target_info.get("label"))
    
    def _on_quest_started(self, quest_info):
        """任务开始处理"""
        self.status_changed.emit(f"任务开始: {quest_info.get('name', '未知任务')}")
    
    def _on_quest_completed(self, quest_info):
        """任务完成处理"""
        self.status_changed.emit(f"任务完成: {quest_info.get('name', '未知任务')}")
    
    def _on_quest_failed(self, quest_info):
        """任务失败处理"""
        self.status_changed.emit(f"任务失败: {quest_info.get('name', '未知任务')}")
    
    def _on_item_collected(self, item_info):
        """物品采集处理"""
        self.state_manager.stats["items_collected"] += 1
        self.status_changed.emit(f"采集物品: {item_info.get('label', '未知物品')}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            "running": self.running,
            "current_task": self.current_task,
            "game_state": self.state_manager.get_game_state().value,
            "battle_state": self.state_manager.battle_state.value,
            "task_info": self.state_manager.get_task_info(),
            "stats": self.state_manager.get_stats()
        }
