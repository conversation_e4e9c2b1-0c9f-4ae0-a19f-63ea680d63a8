# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - A*寻路算法模块
基于A*算法的智能寻路系统
"""

import heapq
import math
import numpy as np
import cv2
from typing import List, Tuple, Optional, Dict, Any
from ..utils.logger import logger

class AStarPathfinder:
    """A*寻路算法类"""
    
    def __init__(self, map_width: int = 1920, map_height: int = 1080):
        self.map_width = map_width
        self.map_height = map_height
        self.grid_size = 10  # 网格大小（像素）
        self.grid_width = map_width // self.grid_size
        self.grid_height = map_height // self.grid_size
        
        # 创建网格地图
        self.grid_map = np.zeros((self.grid_height, self.grid_width), dtype=np.uint8)
        self.obstacle_map = np.zeros((self.grid_height, self.grid_width), dtype=np.uint8)
        
        # 移动方向（8方向）
        self.directions = [
            (-1, -1), (-1, 0), (-1, 1),  # 上左、上、上右
            (0, -1),           (0, 1),   # 左、右
            (1, -1),  (1, 0),  (1, 1)    # 下左、下、下右
        ]
        
        # 对角线移动的代价更高
        self.direction_costs = [
            1.414, 1.0, 1.414,  # 对角线移动代价为√2
            1.0,        1.0,     # 直线移动代价为1
            1.414, 1.0, 1.414
        ]
        
        logger.info(f"A*寻路器初始化完成: 地图尺寸 {map_width}x{map_height}, 网格尺寸 {self.grid_width}x{self.grid_height}")
    
    def world_to_grid(self, x: float, y: float) -> Tuple[int, int]:
        """世界坐标转网格坐标"""
        grid_x = int(x // self.grid_size)
        grid_y = int(y // self.grid_size)
        
        # 确保在网格范围内
        grid_x = max(0, min(grid_x, self.grid_width - 1))
        grid_y = max(0, min(grid_y, self.grid_height - 1))
        
        return grid_x, grid_y
    
    def grid_to_world(self, grid_x: int, grid_y: int) -> Tuple[float, float]:
        """网格坐标转世界坐标"""
        x = (grid_x + 0.5) * self.grid_size
        y = (grid_y + 0.5) * self.grid_size
        return x, y
    
    def is_valid_position(self, grid_x: int, grid_y: int) -> bool:
        """检查网格位置是否有效"""
        if grid_x < 0 or grid_x >= self.grid_width:
            return False
        if grid_y < 0 or grid_y >= self.grid_height:
            return False
        return self.grid_map[grid_y, grid_x] == 0  # 0表示可通行
    
    def heuristic(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """启发式函数（欧几里得距离）"""
        dx = abs(pos1[0] - pos2[0])
        dy = abs(pos1[1] - pos2[1])
        return math.sqrt(dx * dx + dy * dy)
    
    def get_neighbors(self, pos: Tuple[int, int]) -> List[Tuple[Tuple[int, int], float]]:
        """获取邻居节点"""
        neighbors = []
        x, y = pos
        
        for i, (dx, dy) in enumerate(self.directions):
            new_x, new_y = x + dx, y + dy
            
            if self.is_valid_position(new_x, new_y):
                cost = self.direction_costs[i]
                neighbors.append(((new_x, new_y), cost))
        
        return neighbors
    
    def find_path(self, 
                 start: Tuple[float, float], 
                 goal: Tuple[float, float]) -> Optional[List[Tuple[float, float]]]:
        """
        使用A*算法寻找路径
        
        Args:
            start: 起始点世界坐标
            goal: 目标点世界坐标
            
        Returns:
            路径点列表（世界坐标），失败返回None
        """
        # 转换为网格坐标
        start_grid = self.world_to_grid(start[0], start[1])
        goal_grid = self.world_to_grid(goal[0], goal[1])
        
        logger.log_navigation(
            "开始寻路", 
            f"({start[0]:.1f}, {start[1]:.1f})", 
            f"({goal[0]:.1f}, {goal[1]:.1f})"
        )
        
        # 检查起点和终点是否有效
        if not self.is_valid_position(start_grid[0], start_grid[1]):
            logger.warning(f"起始点不可通行: {start_grid}")
            return None
        
        if not self.is_valid_position(goal_grid[0], goal_grid[1]):
            logger.warning(f"目标点不可通行: {goal_grid}")
            return None
        
        # A*算法核心
        open_set = []
        heapq.heappush(open_set, (0, start_grid))
        
        came_from = {}
        g_score = {start_grid: 0}
        f_score = {start_grid: self.heuristic(start_grid, goal_grid)}
        
        closed_set = set()
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current == goal_grid:
                # 找到路径，重构路径
                path = self._reconstruct_path(came_from, current)
                world_path = [self.grid_to_world(x, y) for x, y in path]
                
                logger.log_navigation(
                    "寻路成功", 
                    details=f"路径长度: {len(world_path)}个点"
                )
                
                return world_path
            
            closed_set.add(current)
            
            for neighbor, move_cost in self.get_neighbors(current):
                if neighbor in closed_set:
                    continue
                
                tentative_g_score = g_score[current] + move_cost
                
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + self.heuristic(neighbor, goal_grid)
                    
                    # 如果邻居不在开放集中，添加它
                    if not any(neighbor == item[1] for item in open_set):
                        heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        logger.warning("寻路失败：无法找到路径")
        return None
    
    def _reconstruct_path(self, 
                         came_from: Dict[Tuple[int, int], Tuple[int, int]], 
                         current: Tuple[int, int]) -> List[Tuple[int, int]]:
        """重构路径"""
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        path.reverse()
        return path
    
    def smooth_path(self, path: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """平滑路径（移除不必要的中间点）"""
        if len(path) <= 2:
            return path
        
        smoothed = [path[0]]
        
        i = 0
        while i < len(path) - 1:
            j = len(path) - 1
            
            # 从最远的点开始，找到第一个可以直接到达的点
            while j > i + 1:
                if self._is_line_clear(path[i], path[j]):
                    break
                j -= 1
            
            smoothed.append(path[j])
            i = j
        
        logger.debug(f"路径平滑: {len(path)} -> {len(smoothed)} 个点")
        return smoothed
    
    def _is_line_clear(self, start: Tuple[float, float], end: Tuple[float, float]) -> bool:
        """检查两点之间的直线是否畅通"""
        # 使用Bresenham直线算法检查路径上的每个网格
        start_grid = self.world_to_grid(start[0], start[1])
        end_grid = self.world_to_grid(end[0], end[1])
        
        points = self._bresenham_line(start_grid[0], start_grid[1], end_grid[0], end_grid[1])
        
        for x, y in points:
            if not self.is_valid_position(x, y):
                return False
        
        return True
    
    def _bresenham_line(self, x0: int, y0: int, x1: int, y1: int) -> List[Tuple[int, int]]:
        """Bresenham直线算法"""
        points = []
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        sx = 1 if x0 < x1 else -1
        sy = 1 if y0 < y1 else -1
        err = dx - dy
        
        x, y = x0, y0
        
        while True:
            points.append((x, y))
            
            if x == x1 and y == y1:
                break
            
            e2 = 2 * err
            if e2 > -dy:
                err -= dy
                x += sx
            if e2 < dx:
                err += dx
                y += sy
        
        return points
    
    def update_obstacles(self, obstacles: List[Tuple[int, int, int, int]]):
        """
        更新障碍物
        
        Args:
            obstacles: 障碍物列表，每个元素为(x1, y1, x2, y2)
        """
        # 清空当前障碍物
        self.grid_map.fill(0)
        
        for x1, y1, x2, y2 in obstacles:
            # 转换为网格坐标
            grid_x1, grid_y1 = self.world_to_grid(x1, y1)
            grid_x2, grid_y2 = self.world_to_grid(x2, y2)
            
            # 确保坐标顺序正确
            min_x, max_x = min(grid_x1, grid_x2), max(grid_x1, grid_x2)
            min_y, max_y = min(grid_y1, grid_y2), max(grid_y1, grid_y2)
            
            # 在网格中标记障碍物
            self.grid_map[min_y:max_y+1, min_x:max_x+1] = 1
        
        logger.debug(f"更新了 {len(obstacles)} 个障碍物")
    
    def add_obstacle(self, x1: int, y1: int, x2: int, y2: int):
        """添加单个障碍物"""
        grid_x1, grid_y1 = self.world_to_grid(x1, y1)
        grid_x2, grid_y2 = self.world_to_grid(x2, y2)
        
        min_x, max_x = min(grid_x1, grid_x2), max(grid_x1, grid_x2)
        min_y, max_y = min(grid_y1, grid_y2), max(grid_y1, grid_y2)
        
        self.grid_map[min_y:max_y+1, min_x:max_x+1] = 1
    
    def remove_obstacle(self, x1: int, y1: int, x2: int, y2: int):
        """移除单个障碍物"""
        grid_x1, grid_y1 = self.world_to_grid(x1, y1)
        grid_x2, grid_y2 = self.world_to_grid(x2, y2)
        
        min_x, max_x = min(grid_x1, grid_x2), max(grid_x1, grid_x2)
        min_y, max_y = min(grid_y1, grid_y2), max(grid_y1, grid_y2)
        
        self.grid_map[min_y:max_y+1, min_x:max_x+1] = 0
    
    def get_distance(self, pos1: Tuple[float, float], pos2: Tuple[float, float]) -> float:
        """计算两点间距离"""
        dx = pos1[0] - pos2[0]
        dy = pos1[1] - pos2[1]
        return math.sqrt(dx * dx + dy * dy)
    
    def find_nearest_walkable(self, target: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """找到最近的可行走位置"""
        target_grid = self.world_to_grid(target[0], target[1])
        
        if self.is_valid_position(target_grid[0], target_grid[1]):
            return target
        
        # 螺旋搜索最近的可行走位置
        for radius in range(1, 20):
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if abs(dx) == radius or abs(dy) == radius:  # 只检查边界
                        check_x = target_grid[0] + dx
                        check_y = target_grid[1] + dy
                        
                        if self.is_valid_position(check_x, check_y):
                            return self.grid_to_world(check_x, check_y)
        
        return None
    
    def visualize_grid(self, path: Optional[List[Tuple[float, float]]] = None) -> np.ndarray:
        """可视化网格地图"""
        # 创建可视化图像
        vis_img = np.zeros((self.grid_height, self.grid_width, 3), dtype=np.uint8)
        
        # 绘制障碍物（红色）
        obstacle_mask = self.grid_map == 1
        vis_img[obstacle_mask] = [0, 0, 255]
        
        # 绘制可行走区域（绿色）
        walkable_mask = self.grid_map == 0
        vis_img[walkable_mask] = [0, 255, 0]
        
        # 绘制路径（蓝色）
        if path:
            for i, (x, y) in enumerate(path):
                grid_x, grid_y = self.world_to_grid(x, y)
                if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
                    vis_img[grid_y, grid_x] = [255, 0, 0]  # 蓝色路径
                    
                    # 标记起点和终点
                    if i == 0:
                        cv2.circle(vis_img, (grid_x, grid_y), 2, (0, 255, 255), -1)  # 黄色起点
                    elif i == len(path) - 1:
                        cv2.circle(vis_img, (grid_x, grid_y), 2, (255, 255, 0), -1)  # 青色终点
        
        # 放大图像以便查看
        vis_img = cv2.resize(vis_img, (self.grid_width * 4, self.grid_height * 4), interpolation=cv2.INTER_NEAREST)
        
        return vis_img
    
    def save_grid_map(self, filename: str):
        """保存网格地图到文件"""
        try:
            np.save(filename, self.grid_map)
            logger.info(f"网格地图保存成功: {filename}")
        except Exception as e:
            logger.error(f"保存网格地图失败: {e}")
    
    def load_grid_map(self, filename: str) -> bool:
        """从文件加载网格地图"""
        try:
            self.grid_map = np.load(filename)
            logger.info(f"网格地图加载成功: {filename}")
            return True
        except Exception as e:
            logger.error(f"加载网格地图失败: {e}")
            return False

# 全局寻路器实例
pathfinder = AStarPathfinder()
