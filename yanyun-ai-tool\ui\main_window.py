# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 主窗口界面
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QGroupBox, QPushButton, QLabel, 
                            QTextEdit, QComboBox, QSpinBox, QCheckBox,
                            QProgressBar, QStatusBar, QMenuBar, QAction,
                            QSplitter, QFrame, QGridLayout, QSlider,
                            QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

from ..yanyunauto.utils.logger import logger

class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    start_automation = pyqtSignal(str, dict)  # 开始自动化信号
    stop_automation = pyqtSignal()            # 停止自动化信号
    load_config = pyqtSignal(str)             # 加载配置信号
    save_config = pyqtSignal(str)             # 保存配置信号
    
    def __init__(self, controller):
        super().__init__()
        self.controller = controller
        self.current_status = "就绪"
        self.automation_running = False
        
        # 初始化UI
        self.init_ui()
        self.setup_connections()
        self.setup_timer()
        
        logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("燕云十六声AI自动化工具 v1.0.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置窗口图标
        if os.path.exists("datas/images/icon.ico"):
            self.setWindowIcon(QIcon("datas/images/icon.ico"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 右侧显示区域
        self.create_display_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 应用样式
        self.apply_styles()
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 任务控制组
        task_group = QGroupBox("任务控制")
        task_layout = QVBoxLayout(task_group)
        
        # 任务类型选择
        task_type_layout = QHBoxLayout()
        task_type_layout.addWidget(QLabel("任务类型:"))
        self.task_type_combo = QComboBox()
        self.task_type_combo.addItems([
            "自动战斗", "自动任务", "自动采集", "自动探索", "自定义脚本"
        ])
        task_type_layout.addWidget(self.task_type_combo)
        task_layout.addLayout(task_type_layout)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("开始")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.stop_button = QPushButton("停止")
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.stop_button.setEnabled(False)
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        task_layout.addLayout(button_layout)
        
        control_layout.addWidget(task_group)
        
        # 配置组
        config_group = QGroupBox("配置设置")
        config_layout = QVBoxLayout(config_group)
        
        # 检测设置
        detection_layout = QGridLayout()
        detection_layout.addWidget(QLabel("置信度阈值:"), 0, 0)
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(10, 100)
        self.confidence_slider.setValue(60)
        self.confidence_label = QLabel("0.60")
        detection_layout.addWidget(self.confidence_slider, 0, 1)
        detection_layout.addWidget(self.confidence_label, 0, 2)
        
        detection_layout.addWidget(QLabel("检测间隔(ms):"), 1, 0)
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(50, 1000)
        self.interval_spinbox.setValue(100)
        detection_layout.addWidget(self.interval_spinbox, 1, 1, 1, 2)
        
        config_layout.addLayout(detection_layout)
        
        # 功能开关
        switches_layout = QVBoxLayout()
        self.auto_battle_check = QCheckBox("自动战斗")
        self.auto_battle_check.setChecked(True)
        self.auto_dodge_check = QCheckBox("自动闪避")
        self.auto_dodge_check.setChecked(True)
        self.auto_skill_check = QCheckBox("自动技能")
        self.auto_skill_check.setChecked(True)
        self.show_overlay_check = QCheckBox("显示检测框")
        self.show_overlay_check.setChecked(True)
        
        switches_layout.addWidget(self.auto_battle_check)
        switches_layout.addWidget(self.auto_dodge_check)
        switches_layout.addWidget(self.auto_skill_check)
        switches_layout.addWidget(self.show_overlay_check)
        
        config_layout.addLayout(switches_layout)
        control_layout.addWidget(config_group)
        
        # 状态信息组
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        # 当前状态
        self.status_label = QLabel(f"状态: {self.current_status}")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; color: #2196F3; }")
        status_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        # 统计信息
        stats_layout = QGridLayout()
        stats_layout.addWidget(QLabel("战斗胜利:"), 0, 0)
        self.battles_won_label = QLabel("0")
        stats_layout.addWidget(self.battles_won_label, 0, 1)
        
        stats_layout.addWidget(QLabel("任务完成:"), 1, 0)
        self.quests_completed_label = QLabel("0")
        stats_layout.addWidget(self.quests_completed_label, 1, 1)
        
        stats_layout.addWidget(QLabel("物品采集:"), 2, 0)
        self.items_collected_label = QLabel("0")
        stats_layout.addWidget(self.items_collected_label, 2, 1)
        
        stats_layout.addWidget(QLabel("运行时间:"), 3, 0)
        self.runtime_label = QLabel("00:00:00")
        stats_layout.addWidget(self.runtime_label, 3, 1)
        
        status_layout.addLayout(stats_layout)
        control_layout.addWidget(status_group)
        
        # 添加弹性空间
        control_layout.addStretch()
        
        parent.addWidget(control_widget)
    
    def create_display_area(self, parent):
        """创建右侧显示区域"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 实时画面标签页
        self.create_live_view_tab()
        
        # 日志标签页
        self.create_log_tab()
        
        # 配置标签页
        self.create_config_tab()
        
        display_layout.addWidget(self.tab_widget)
        parent.addWidget(display_widget)
    
    def create_live_view_tab(self):
        """创建实时画面标签页"""
        live_widget = QWidget()
        live_layout = QVBoxLayout(live_widget)
        
        # 画面显示区域
        self.image_label = QLabel()
        self.image_label.setMinimumSize(640, 360)
        self.image_label.setStyleSheet("QLabel { border: 1px solid #ccc; background-color: #f0f0f0; }")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setText("等待游戏画面...")
        
        live_layout.addWidget(self.image_label)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        self.screenshot_button = QPushButton("截图")
        self.save_image_button = QPushButton("保存图像")
        self.refresh_button = QPushButton("刷新")
        
        control_layout.addWidget(self.screenshot_button)
        control_layout.addWidget(self.save_image_button)
        control_layout.addWidget(self.refresh_button)
        control_layout.addStretch()
        
        live_layout.addLayout(control_layout)
        
        self.tab_widget.addTab(live_widget, "实时画面")
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        
        log_layout.addWidget(self.log_text)
        
        # 日志控制
        log_control_layout = QHBoxLayout()
        self.clear_log_button = QPushButton("清空日志")
        self.save_log_button = QPushButton("保存日志")
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        
        log_control_layout.addWidget(self.clear_log_button)
        log_control_layout.addWidget(self.save_log_button)
        log_control_layout.addWidget(self.auto_scroll_check)
        log_control_layout.addStretch()
        
        log_layout.addLayout(log_control_layout)
        
        self.tab_widget.addTab(log_widget, "日志")
    
    def create_config_tab(self):
        """创建配置标签页"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        
        # 配置文件操作
        file_layout = QHBoxLayout()
        self.load_config_button = QPushButton("加载配置")
        self.save_config_button = QPushButton("保存配置")
        self.reset_config_button = QPushButton("重置配置")
        
        file_layout.addWidget(self.load_config_button)
        file_layout.addWidget(self.save_config_button)
        file_layout.addWidget(self.reset_config_button)
        file_layout.addStretch()
        
        config_layout.addLayout(file_layout)
        
        # 配置编辑区域
        self.config_text = QTextEdit()
        self.config_text.setFont(QFont("Consolas", 10))
        config_layout.addWidget(self.config_text)
        
        self.tab_widget.addTab(config_widget, "配置")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        load_action = QAction('加载配置', self)
        load_action.triggered.connect(self.load_config_file)
        file_menu.addAction(load_action)
        
        save_action = QAction('保存配置', self)
        save_action.triggered.connect(self.save_config_file)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        
        screenshot_action = QAction('截图', self)
        screenshot_action.triggered.connect(self.take_screenshot)
        tools_menu.addAction(screenshot_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 添加状态信息
        self.status_bar.showMessage("就绪")
        
        # 添加永久部件
        self.fps_label = QLabel("FPS: 0")
        self.status_bar.addPermanentWidget(self.fps_label)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px 15px;
                background-color: #ffffff;
            }
            QPushButton:hover {
                background-color: #e6e6e6;
            }
            QPushButton:pressed {
                background-color: #d4d4d4;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e6e6e6;
                border: 1px solid #cccccc;
                padding: 5px 10px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.start_button.clicked.connect(self.start_automation_clicked)
        self.stop_button.clicked.connect(self.stop_automation_clicked)
        self.screenshot_button.clicked.connect(self.take_screenshot)
        self.clear_log_button.clicked.connect(self.clear_log)
        self.save_log_button.clicked.connect(self.save_log)
        self.load_config_button.clicked.connect(self.load_config_file)
        self.save_config_button.clicked.connect(self.save_config_file)
        
        # 滑块连接
        self.confidence_slider.valueChanged.connect(self.update_confidence_label)
    
    def setup_timer(self):
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def start_automation_clicked(self):
        """开始自动化按钮点击"""
        task_type_map = {
            "自动战斗": "auto_battle",
            "自动任务": "auto_quest", 
            "自动采集": "auto_collection",
            "自动探索": "auto_exploration",
            "自定义脚本": "custom_script"
        }
        
        task_type = task_type_map.get(self.task_type_combo.currentText(), "auto_battle")
        
        # 收集配置参数
        config = {
            "confidence_threshold": self.confidence_slider.value() / 100.0,
            "detection_interval": self.interval_spinbox.value(),
            "auto_battle": self.auto_battle_check.isChecked(),
            "auto_dodge": self.auto_dodge_check.isChecked(),
            "auto_skill": self.auto_skill_check.isChecked(),
            "show_overlay": self.show_overlay_check.isChecked()
        }
        
        self.start_automation.emit(task_type, config)
        self.automation_running = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
    
    def stop_automation_clicked(self):
        """停止自动化按钮点击"""
        self.stop_automation.emit()
        self.automation_running = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
    
    def update_confidence_label(self, value):
        """更新置信度标签"""
        self.confidence_label.setText(f"{value/100:.2f}")
    
    def take_screenshot(self):
        """截图"""
        # TODO: 实现截图功能
        self.add_log_message("截图功能待实现")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def save_log(self):
        """保存日志"""
        filename, _ = QFileDialog.getSaveFileName(self, "保存日志", "log.txt", "Text Files (*.txt)")
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.add_log_message(f"日志已保存到: {filename}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存日志失败: {e}")
    
    def load_config_file(self):
        """加载配置文件"""
        filename, _ = QFileDialog.getOpenFileName(self, "加载配置", "", "Config Files (*.ini *.json)")
        if filename:
            self.load_config.emit(filename)
    
    def save_config_file(self):
        """保存配置文件"""
        filename, _ = QFileDialog.getSaveFileName(self, "保存配置", "config.ini", "Config Files (*.ini *.json)")
        if filename:
            self.save_config.emit(filename)
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "燕云十六声AI自动化工具 v1.0.0\n\n"
                         "基于计算机视觉和AI技术的游戏自动化框架\n"
                         "支持自动战斗、任务执行、资源采集等功能")
    
    def update_display(self):
        """更新显示"""
        if hasattr(self.controller, 'get_current_status'):
            status = self.controller.get_current_status()
            
            # 更新统计信息
            stats = status.get('stats', {})
            self.battles_won_label.setText(str(stats.get('battles_won', 0)))
            self.quests_completed_label.setText(str(stats.get('quests_completed', 0)))
            self.items_collected_label.setText(str(stats.get('items_collected', 0)))
    
    def update_status(self, status):
        """更新状态"""
        self.current_status = status
        self.status_label.setText(f"状态: {status}")
        self.status_bar.showMessage(status)
    
    def add_log_message(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        if self.auto_scroll_check.isChecked():
            self.log_text.moveCursor(self.log_text.textCursor().End)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.automation_running:
            reply = QMessageBox.question(self, '确认退出', 
                                       '自动化正在运行中，确定要退出吗？',
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.stop_automation.emit()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
