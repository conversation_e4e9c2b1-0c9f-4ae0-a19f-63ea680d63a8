# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 截图模块
提供高效的屏幕截图功能
"""

import cv2
import numpy as np
import win32gui
import win32ui
import win32con
import win32api
from ctypes import windll
import time
from typing import Optional, Tuple
from ..utils.logger import logger

class ScreenCapture:
    """屏幕截图类"""
    
    def __init__(self, window_title: str = "燕云十六声"):
        self.window_title = window_title
        self.window_handle = None
        self.window_rect = None
        self.last_capture_time = 0
        self.capture_interval = 0.033  # 约30fps
        
        # 查找游戏窗口
        self.find_game_window()
    
    def find_game_window(self) -> bool:
        """查找游戏窗口"""
        try:
            # 尝试多种可能的窗口类名
            window_classes = ["UnityWndClass", "Unity", "YanyunWindow"]
            
            for window_class in window_classes:
                self.window_handle = win32gui.FindWindow(window_class, self.window_title)
                if self.window_handle:
                    break
            
            if not self.window_handle:
                # 通过窗口标题查找
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if self.window_title in window_text:
                            windows.append(hwnd)
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                if windows:
                    self.window_handle = windows[0]
            
            if self.window_handle:
                self.update_window_rect()
                logger.info(f"找到游戏窗口: {self.window_title} (句柄: {self.window_handle})")
                return True
            else:
                logger.warning(f"未找到游戏窗口: {self.window_title}")
                return False
                
        except Exception as e:
            logger.error(f"查找游戏窗口失败: {e}")
            return False
    
    def update_window_rect(self):
        """更新窗口矩形区域"""
        if self.window_handle:
            try:
                self.window_rect = win32gui.GetWindowRect(self.window_handle)
                logger.debug(f"窗口矩形: {self.window_rect}")
            except Exception as e:
                logger.error(f"获取窗口矩形失败: {e}")
    
    def activate_window(self) -> bool:
        """激活游戏窗口"""
        if not self.window_handle:
            return False
        
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(self.window_handle):
                win32gui.ShowWindow(self.window_handle, win32con.SW_RESTORE)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.1)
            return True
            
        except Exception as e:
            logger.error(f"激活窗口失败: {e}")
            return False
    
    def capture_window(self, 
                      region: Optional[Tuple[int, int, int, int]] = None) -> Optional[np.ndarray]:
        """
        截取窗口图像
        
        Args:
            region: 截取区域 (left, top, right, bottom)，None表示整个窗口
            
        Returns:
            截图图像数组，失败返回None
        """
        if not self.window_handle:
            if not self.find_game_window():
                return None
        
        try:
            # 检查截图间隔
            current_time = time.time()
            if current_time - self.last_capture_time < self.capture_interval:
                time.sleep(self.capture_interval - (current_time - self.last_capture_time))
            
            # 更新窗口矩形
            self.update_window_rect()
            
            if not self.window_rect:
                return None
            
            # 计算截图区域
            if region:
                left, top, right, bottom = region
            else:
                left, top, right, bottom = self.window_rect
            
            width = right - left
            height = bottom - top
            
            if width <= 0 or height <= 0:
                logger.warning(f"无效的截图区域: {width}x{height}")
                return None
            
            # 获取窗口设备上下文
            hwnd_dc = win32gui.GetWindowDC(self.window_handle)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()
            
            # 创建位图
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
            save_dc.SelectObject(save_bitmap)
            
            # 复制窗口内容到位图
            if region:
                # 截取指定区域
                save_dc.BitBlt((0, 0), (width, height), mfc_dc, 
                              (left - self.window_rect[0], top - self.window_rect[1]), 
                              win32con.SRCCOPY)
            else:
                # 截取整个窗口
                save_dc.BitBlt((0, 0), (width, height), mfc_dc, (0, 0), win32con.SRCCOPY)
            
            # 获取位图数据
            bmp_info = save_bitmap.GetInfo()
            bmp_str = save_bitmap.GetBitmapBits(True)
            
            # 转换为numpy数组
            img = np.frombuffer(bmp_str, dtype='uint8')
            img.shape = (height, width, 4)  # BGRA格式
            
            # 转换为BGR格式
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            win32gui.DeleteObject(save_bitmap.GetHandle())
            save_dc.DeleteDC()
            mfc_dc.DeleteDC()
            win32gui.ReleaseDC(self.window_handle, hwnd_dc)
            
            self.last_capture_time = time.time()
            return img
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> Optional[np.ndarray]:
        """
        截取屏幕图像
        
        Args:
            region: 截取区域 (left, top, right, bottom)，None表示整个屏幕
            
        Returns:
            截图图像数组，失败返回None
        """
        try:
            if region:
                left, top, right, bottom = region
            else:
                left, top = 0, 0
                right = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
                bottom = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
            
            width = right - left
            height = bottom - top
            
            # 获取屏幕设备上下文
            hdesktop = win32gui.GetDesktopWindow()
            hwnd_dc = win32gui.GetWindowDC(hdesktop)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()
            
            # 创建位图
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
            save_dc.SelectObject(save_bitmap)
            
            # 复制屏幕内容到位图
            save_dc.BitBlt((0, 0), (width, height), mfc_dc, (left, top), win32con.SRCCOPY)
            
            # 获取位图数据
            bmp_info = save_bitmap.GetInfo()
            bmp_str = save_bitmap.GetBitmapBits(True)
            
            # 转换为numpy数组
            img = np.frombuffer(bmp_str, dtype='uint8')
            img.shape = (height, width, 4)  # BGRA格式
            
            # 转换为BGR格式
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            win32gui.DeleteObject(save_bitmap.GetHandle())
            save_dc.DeleteDC()
            mfc_dc.DeleteDC()
            win32gui.ReleaseDC(hdesktop, hwnd_dc)
            
            return img
            
        except Exception as e:
            logger.error(f"屏幕截图失败: {e}")
            return None
    
    def save_screenshot(self, 
                       filename: str, 
                       region: Optional[Tuple[int, int, int, int]] = None,
                       use_window: bool = True) -> bool:
        """
        保存截图到文件
        
        Args:
            filename: 保存文件名
            region: 截取区域
            use_window: 是否使用窗口截图
            
        Returns:
            是否保存成功
        """
        try:
            if use_window:
                img = self.capture_window(region)
            else:
                img = self.capture_screen(region)
            
            if img is not None:
                cv2.imencode('.png', img)[1].tofile(filename)
                logger.info(f"截图保存成功: {filename}")
                return True
            else:
                logger.error("截图失败，无法保存")
                return False
                
        except Exception as e:
            logger.error(f"保存截图失败: {e}")
            return False
    
    def get_window_info(self) -> dict:
        """获取窗口信息"""
        info = {
            "handle": self.window_handle,
            "title": self.window_title,
            "rect": self.window_rect,
            "is_visible": False,
            "is_active": False
        }
        
        if self.window_handle:
            try:
                info["is_visible"] = win32gui.IsWindowVisible(self.window_handle)
                info["is_active"] = win32gui.GetForegroundWindow() == self.window_handle
                info["actual_title"] = win32gui.GetWindowText(self.window_handle)
            except Exception as e:
                logger.error(f"获取窗口信息失败: {e}")
        
        return info
    
    def set_capture_fps(self, fps: int):
        """设置截图帧率"""
        if fps > 0:
            self.capture_interval = 1.0 / fps
            logger.info(f"截图帧率设置为: {fps} FPS")

# 全局截图实例
screen_capture = ScreenCapture()

def screenshot(region: Optional[Tuple[int, int, int, int]] = None, 
              use_window: bool = True) -> Optional[np.ndarray]:
    """
    便捷的截图函数
    
    Args:
        region: 截取区域
        use_window: 是否使用窗口截图
        
    Returns:
        截图图像数组
    """
    if use_window:
        return screen_capture.capture_window(region)
    else:
        return screen_capture.capture_screen(region)
