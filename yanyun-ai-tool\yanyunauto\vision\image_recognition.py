# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - 图像识别模块
基于OpenCV的模板匹配和特征检测
"""

import cv2
import numpy as np
import os
import time
from typing import List, Tuple, Optional, Dict, Any
from ..utils.logger import logger

class ImageRecognition:
    """图像识别类"""
    
    def __init__(self, template_dir="datas/images"):
        self.template_dir = template_dir
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """加载模板图像"""
        if not os.path.exists(self.template_dir):
            logger.warning(f"模板目录不存在: {self.template_dir}")
            return
        
        template_count = 0
        for filename in os.listdir(self.template_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                template_path = os.path.join(self.template_dir, filename)
                template_name = os.path.splitext(filename)[0]
                
                try:
                    template = cv2.imdecode(
                        np.fromfile(template_path, dtype=np.uint8), 
                        cv2.IMREAD_UNCHANGED
                    )
                    if template is not None:
                        self.templates[template_name] = template
                        template_count += 1
                except Exception as e:
                    logger.error(f"加载模板失败 {filename}: {e}")
        
        logger.info(f"加载了 {template_count} 个模板图像")
    
    def find_template(self, 
                     screenshot: np.ndarray, 
                     template_name: str, 
                     threshold: float = 0.8,
                     method: int = cv2.TM_CCOEFF_NORMED,
                     mask: Optional[np.ndarray] = None) -> List[Dict[str, Any]]:
        """
        在截图中查找模板
        
        Args:
            screenshot: 截图图像
            template_name: 模板名称
            threshold: 匹配阈值
            method: 匹配方法
            mask: 掩码
            
        Returns:
            匹配结果列表
        """
        if template_name not in self.templates:
            logger.warning(f"模板不存在: {template_name}")
            return []
        
        template = self.templates[template_name]
        
        try:
            # 执行模板匹配
            if mask is not None:
                result = cv2.matchTemplate(screenshot, template, method, mask=mask)
            else:
                result = cv2.matchTemplate(screenshot, template, method)
            
            # 查找所有匹配位置
            locations = np.where(result >= threshold)
            matches = []
            
            h, w = template.shape[:2]
            
            for pt in zip(*locations[::-1]):
                confidence = result[pt[1], pt[0]]
                center_x = pt[0] + w // 2
                center_y = pt[1] + h // 2
                
                match_info = {
                    "template": template_name,
                    "confidence": float(confidence),
                    "position": (int(center_x), int(center_y)),
                    "bbox": (int(pt[0]), int(pt[1]), int(pt[0] + w), int(pt[1] + h)),
                    "size": (w, h)
                }
                matches.append(match_info)
            
            # 按置信度排序
            matches.sort(key=lambda x: x["confidence"], reverse=True)
            
            if matches:
                logger.log_detection(
                    template_name, 
                    matches[0]["confidence"], 
                    matches[0]["position"]
                )
            
            return matches
            
        except Exception as e:
            logger.error(f"模板匹配失败 {template_name}: {e}")
            return []
    
    def find_multiple_templates(self, 
                               screenshot: np.ndarray, 
                               template_names: List[str], 
                               threshold: float = 0.8) -> Dict[str, List[Dict[str, Any]]]:
        """
        在截图中查找多个模板
        
        Args:
            screenshot: 截图图像
            template_names: 模板名称列表
            threshold: 匹配阈值
            
        Returns:
            每个模板的匹配结果
        """
        results = {}
        for template_name in template_names:
            results[template_name] = self.find_template(screenshot, template_name, threshold)
        return results
    
    def find_best_match(self, 
                       screenshot: np.ndarray, 
                       template_names: List[str], 
                       threshold: float = 0.8) -> Optional[Dict[str, Any]]:
        """
        查找最佳匹配的模板
        
        Args:
            screenshot: 截图图像
            template_names: 模板名称列表
            threshold: 匹配阈值
            
        Returns:
            最佳匹配结果
        """
        best_match = None
        best_confidence = 0
        
        for template_name in template_names:
            matches = self.find_template(screenshot, template_name, threshold)
            if matches and matches[0]["confidence"] > best_confidence:
                best_match = matches[0]
                best_confidence = matches[0]["confidence"]
        
        return best_match
    
    def detect_ui_elements(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """
        检测UI元素
        
        Args:
            screenshot: 截图图像
            
        Returns:
            UI元素检测结果
        """
        ui_elements = {
            "buttons": [],
            "menus": [],
            "dialogs": [],
            "health_bar": None,
            "mana_bar": None,
            "minimap": None
        }
        
        # 定义UI元素模板
        ui_templates = {
            "buttons": ["button_confirm", "button_cancel", "button_ok", "button_close"],
            "menus": ["menu_main", "menu_inventory", "menu_skills", "menu_map"],
            "dialogs": ["dialog_quest", "dialog_shop", "dialog_npc"],
            "health_bar": ["health_bar", "hp_bar"],
            "mana_bar": ["mana_bar", "mp_bar"],
            "minimap": ["minimap", "small_map"]
        }
        
        for element_type, templates in ui_templates.items():
            if element_type in ["health_bar", "mana_bar", "minimap"]:
                # 单个元素
                best_match = self.find_best_match(screenshot, templates, 0.7)
                ui_elements[element_type] = best_match
            else:
                # 多个元素
                for template in templates:
                    matches = self.find_template(screenshot, template, 0.7)
                    ui_elements[element_type].extend(matches)
        
        return ui_elements
    
    def detect_game_objects(self, screenshot: np.ndarray) -> Dict[str, List[Dict[str, Any]]]:
        """
        检测游戏对象
        
        Args:
            screenshot: 截图图像
            
        Returns:
            游戏对象检测结果
        """
        game_objects = {
            "enemies": [],
            "npcs": [],
            "items": [],
            "chests": [],
            "portals": []
        }
        
        # 定义游戏对象模板
        object_templates = {
            "enemies": ["enemy_normal", "enemy_elite", "enemy_boss"],
            "npcs": ["npc_merchant", "npc_quest", "npc_guard"],
            "items": ["item_weapon", "item_armor", "item_consumable"],
            "chests": ["chest_normal", "chest_rare", "chest_epic"],
            "portals": ["portal_teleport", "portal_dungeon"]
        }
        
        for object_type, templates in object_templates.items():
            for template in templates:
                matches = self.find_template(screenshot, template, 0.6)
                for match in matches:
                    match["object_type"] = object_type
                    match["subtype"] = template
                game_objects[object_type].extend(matches)
        
        return game_objects
    
    def analyze_battle_scene(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """
        分析战斗场景
        
        Args:
            screenshot: 截图图像
            
        Returns:
            战斗场景分析结果
        """
        battle_info = {
            "in_battle": False,
            "enemies": [],
            "player_status": {},
            "skills_available": [],
            "battle_ui": {}
        }
        
        # 检测战斗UI
        battle_ui_templates = ["battle_ui", "skill_bar", "target_frame"]
        battle_ui_matches = self.find_multiple_templates(screenshot, battle_ui_templates, 0.7)
        
        # 判断是否在战斗中
        battle_info["in_battle"] = any(matches for matches in battle_ui_matches.values())
        
        if battle_info["in_battle"]:
            # 检测敌人
            enemy_templates = ["enemy_normal", "enemy_elite", "enemy_boss", "enemy_target"]
            enemy_matches = self.find_multiple_templates(screenshot, enemy_templates, 0.6)
            for template, matches in enemy_matches.items():
                battle_info["enemies"].extend(matches)
            
            # 检测技能
            skill_templates = ["skill_1", "skill_2", "skill_3", "skill_4", "skill_ultimate"]
            skill_matches = self.find_multiple_templates(screenshot, skill_templates, 0.7)
            for template, matches in skill_matches.items():
                if matches:
                    battle_info["skills_available"].append(template)
            
            # 检测玩家状态
            status_templates = ["low_health", "low_mana", "buff_icon", "debuff_icon"]
            status_matches = self.find_multiple_templates(screenshot, status_templates, 0.7)
            battle_info["player_status"] = status_matches
        
        return battle_info
    
    def get_template_list(self) -> List[str]:
        """获取可用模板列表"""
        return list(self.templates.keys())
    
    def add_template(self, name: str, image: np.ndarray):
        """添加新模板"""
        self.templates[name] = image.copy()
        logger.info(f"添加新模板: {name}")
    
    def remove_template(self, name: str):
        """移除模板"""
        if name in self.templates:
            del self.templates[name]
            logger.info(f"移除模板: {name}")
    
    def save_template(self, name: str, image: np.ndarray, save_path: Optional[str] = None):
        """保存模板到文件"""
        if save_path is None:
            save_path = os.path.join(self.template_dir, f"{name}.png")
        
        try:
            cv2.imencode('.png', image)[1].tofile(save_path)
            self.add_template(name, image)
            logger.info(f"模板保存成功: {save_path}")
        except Exception as e:
            logger.error(f"保存模板失败: {e}")

# 全局图像识别实例
image_recognition = ImageRecognition()
