# -*- coding: utf-8 -*-
"""
燕云十六声AI工具 - YOLO目标检测模块
基于ONNX Runtime的YOLOv5目标检测
"""

import cv2
import numpy as np
import onnxruntime as ort
import time
import random
from typing import List, Dict, Any, Optional, Tuple
from ..utils.logger import logger

class YoloDetector:
    """YOLO目标检测器"""
    
    def __init__(self, 
                 model_path: str = "datas/models/yanyun_yolov5.onnx",
                 input_size: Tuple[int, int] = (640, 640),
                 confidence_threshold: float = 0.6,
                 nms_threshold: float = 0.4,
                 providers: List[str] = None):
        """
        初始化YOLO检测器
        
        Args:
            model_path: ONNX模型路径
            input_size: 输入图像尺寸
            confidence_threshold: 置信度阈值
            nms_threshold: NMS阈值
            providers: ONNX Runtime提供者
        """
        self.model_path = model_path
        self.input_size = input_size
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        
        # 设置ONNX Runtime提供者
        if providers is None:
            providers = ["CUDAExecutionProvider", "CPUExecutionProvider"]
        self.providers = providers
        
        # 燕云十六声游戏对象标签
        self.labels = {
            0: "玩家",
            1: "敌人",
            2: "NPC",
            3: "宝箱",
            4: "采集物",
            5: "传送点",
            6: "任务目标",
            7: "装备",
            8: "消耗品",
            9: "技能图标",
            10: "UI按钮",
            11: "血条",
            12: "蓝条",
            13: "小地图",
            14: "对话框",
            15: "菜单",
            16: "BOSS",
            17: "精英怪",
            18: "普通怪",
            19: "可交互物体",
            20: "危险区域"
        }
        
        # 为每个类别生成随机颜色
        self.colors = {}
        for label_id in self.labels.keys():
            self.colors[label_id] = [random.randint(0, 255) for _ in range(3)]
        
        self.session = None
        self.input_name = None
        self.output_names = None
        
        # 加载模型
        self.load_model()
    
    def load_model(self) -> bool:
        """加载ONNX模型"""
        try:
            # 创建ONNX Runtime会话
            self.session = ort.InferenceSession(
                self.model_path, 
                providers=self.providers
            )
            
            # 获取输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
            
            logger.info(f"YOLO模型加载成功: {self.model_path}")
            logger.info(f"输入名称: {self.input_name}")
            logger.info(f"输出名称: {self.output_names}")
            logger.info(f"使用提供者: {self.session.get_providers()}")
            
            return True
            
        except Exception as e:
            logger.error(f"加载YOLO模型失败: {e}")
            return False
    
    def preprocess_image(self, image: np.ndarray) -> Tuple[np.ndarray, float, Tuple[int, int]]:
        """
        预处理图像
        
        Args:
            image: 输入图像
            
        Returns:
            预处理后的图像、缩放比例、填充偏移
        """
        # 获取原始尺寸
        original_height, original_width = image.shape[:2]
        target_width, target_height = self.input_size
        
        # 计算缩放比例
        scale = min(target_width / original_width, target_height / original_height)
        
        # 计算新尺寸
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        
        # 缩放图像
        resized_image = cv2.resize(image, (new_width, new_height))
        
        # 创建目标尺寸的图像并填充
        padded_image = np.full((target_height, target_width, 3), 114, dtype=np.uint8)
        
        # 计算填充偏移
        pad_x = (target_width - new_width) // 2
        pad_y = (target_height - new_height) // 2
        
        # 将缩放后的图像放置到中心
        padded_image[pad_y:pad_y + new_height, pad_x:pad_x + new_width] = resized_image
        
        # 转换为模型输入格式
        input_image = padded_image.astype(np.float32) / 255.0
        input_image = np.transpose(input_image, (2, 0, 1))  # HWC -> CHW
        input_image = np.expand_dims(input_image, axis=0)   # 添加batch维度
        
        return input_image, scale, (pad_x, pad_y)
    
    def postprocess_detections(self, 
                             outputs: List[np.ndarray], 
                             scale: float, 
                             pad_offset: Tuple[int, int],
                             original_shape: Tuple[int, int]) -> List[Dict[str, Any]]:
        """
        后处理检测结果
        
        Args:
            outputs: 模型输出
            scale: 缩放比例
            pad_offset: 填充偏移
            original_shape: 原始图像尺寸
            
        Returns:
            检测结果列表
        """
        detections = []
        
        if not outputs:
            return detections
        
        # 获取检测结果
        predictions = outputs[0]  # 假设第一个输出是检测结果
        
        # 解析预测结果 [batch, num_detections, 85] (x, y, w, h, conf, class_probs...)
        for detection in predictions[0]:  # 取第一个batch
            # 提取置信度
            confidence = detection[4]
            
            if confidence < self.confidence_threshold:
                continue
            
            # 提取类别概率
            class_probs = detection[5:]
            class_id = np.argmax(class_probs)
            class_confidence = class_probs[class_id]
            
            # 最终置信度
            final_confidence = confidence * class_confidence
            
            if final_confidence < self.confidence_threshold:
                continue
            
            # 提取边界框坐标 (中心点格式)
            center_x, center_y, width, height = detection[:4]
            
            # 转换为左上角坐标格式
            x1 = center_x - width / 2
            y1 = center_y - height / 2
            x2 = center_x + width / 2
            y2 = center_y + height / 2
            
            # 转换回原始图像坐标
            pad_x, pad_y = pad_offset
            x1 = (x1 - pad_x) / scale
            y1 = (y1 - pad_y) / scale
            x2 = (x2 - pad_x) / scale
            y2 = (y2 - pad_y) / scale
            
            # 限制在图像范围内
            original_height, original_width = original_shape
            x1 = max(0, min(x1, original_width))
            y1 = max(0, min(y1, original_height))
            x2 = max(0, min(x2, original_width))
            y2 = max(0, min(y2, original_height))
            
            # 计算中心点和尺寸
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            bbox_width = x2 - x1
            bbox_height = y2 - y1
            
            # 创建检测结果
            detection_result = {
                "label": self.labels.get(class_id, f"unknown_{class_id}"),
                "class_id": int(class_id),
                "confidence": float(final_confidence),
                "bbox": [int(x1), int(y1), int(x2), int(y2)],
                "center": [int(center_x), int(center_y)],
                "size": [int(bbox_width), int(bbox_height)],
                "area": int(bbox_width * bbox_height)
            }
            
            detections.append(detection_result)
        
        # 应用非极大值抑制
        detections = self.apply_nms(detections)
        
        return detections
    
    def apply_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用非极大值抑制
        
        Args:
            detections: 检测结果列表
            
        Returns:
            NMS后的检测结果
        """
        if not detections:
            return detections
        
        # 按置信度排序
        detections.sort(key=lambda x: x["confidence"], reverse=True)
        
        # 按类别分组应用NMS
        class_groups = {}
        for detection in detections:
            class_id = detection["class_id"]
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(detection)
        
        final_detections = []
        
        for class_id, class_detections in class_groups.items():
            # 对每个类别应用NMS
            nms_detections = self._nms_single_class(class_detections)
            final_detections.extend(nms_detections)
        
        return final_detections
    
    def _nms_single_class(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对单个类别应用NMS"""
        if not detections:
            return detections
        
        # 提取边界框和置信度
        boxes = np.array([det["bbox"] for det in detections])
        scores = np.array([det["confidence"] for det in detections])
        
        # 使用OpenCV的NMS
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), 
            scores.tolist(), 
            self.confidence_threshold, 
            self.nms_threshold
        )
        
        if len(indices) > 0:
            indices = indices.flatten()
            return [detections[i] for i in indices]
        else:
            return []
    
    def detect(self, 
              image: np.ndarray, 
              draw_results: bool = False) -> Tuple[List[Dict[str, Any]], Optional[np.ndarray]]:
        """
        执行目标检测
        
        Args:
            image: 输入图像
            draw_results: 是否绘制检测结果
            
        Returns:
            检测结果列表和可选的绘制图像
        """
        if self.session is None:
            logger.error("YOLO模型未加载")
            return [], None
        
        start_time = time.time()
        
        try:
            # 预处理图像
            input_image, scale, pad_offset = self.preprocess_image(image)
            
            # 执行推理
            outputs = self.session.run(
                self.output_names, 
                {self.input_name: input_image}
            )
            
            # 后处理结果
            detections = self.postprocess_detections(
                outputs, scale, pad_offset, image.shape[:2]
            )
            
            # 记录检测结果
            inference_time = (time.time() - start_time) * 1000
            logger.debug(f"YOLO检测完成: {len(detections)}个目标, 耗时: {inference_time:.2f}ms")
            
            # 绘制结果
            result_image = None
            if draw_results and detections:
                result_image = self.draw_detections(image.copy(), detections)
            
            return detections, result_image
            
        except Exception as e:
            logger.error(f"YOLO检测失败: {e}")
            return [], None
    
    def draw_detections(self, 
                       image: np.ndarray, 
                       detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果列表
            
        Returns:
            绘制了检测结果的图像
        """
        for detection in detections:
            # 获取信息
            label = detection["label"]
            confidence = detection["confidence"]
            bbox = detection["bbox"]
            class_id = detection["class_id"]
            
            # 获取颜色
            color = self.colors.get(class_id, [0, 255, 0])
            
            # 绘制边界框
            cv2.rectangle(image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
            
            # 绘制标签
            label_text = f"{label}: {confidence:.2f}"
            label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            # 绘制标签背景
            cv2.rectangle(
                image, 
                (bbox[0], bbox[1] - label_size[1] - 10),
                (bbox[0] + label_size[0], bbox[1]),
                color, 
                -1
            )
            
            # 绘制标签文字
            cv2.putText(
                image, 
                label_text,
                (bbox[0], bbox[1] - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (255, 255, 255),
                2
            )
            
            # 绘制中心点
            center = detection["center"]
            cv2.circle(image, (center[0], center[1]), 3, color, -1)
        
        return image
    
    def filter_detections(self, 
                         detections: List[Dict[str, Any]], 
                         labels: List[str] = None,
                         min_confidence: float = None,
                         min_area: int = None) -> List[Dict[str, Any]]:
        """
        过滤检测结果
        
        Args:
            detections: 检测结果列表
            labels: 要保留的标签列表
            min_confidence: 最小置信度
            min_area: 最小面积
            
        Returns:
            过滤后的检测结果
        """
        filtered = detections.copy()
        
        # 按标签过滤
        if labels:
            filtered = [det for det in filtered if det["label"] in labels]
        
        # 按置信度过滤
        if min_confidence is not None:
            filtered = [det for det in filtered if det["confidence"] >= min_confidence]
        
        # 按面积过滤
        if min_area is not None:
            filtered = [det for det in filtered if det["area"] >= min_area]
        
        return filtered
    
    def get_closest_detection(self, 
                            detections: List[Dict[str, Any]], 
                            target_point: Tuple[int, int]) -> Optional[Dict[str, Any]]:
        """
        获取距离目标点最近的检测结果
        
        Args:
            detections: 检测结果列表
            target_point: 目标点坐标
            
        Returns:
            最近的检测结果
        """
        if not detections:
            return None
        
        min_distance = float('inf')
        closest_detection = None
        
        for detection in detections:
            center = detection["center"]
            distance = np.sqrt(
                (center[0] - target_point[0]) ** 2 + 
                (center[1] - target_point[1]) ** 2
            )
            
            if distance < min_distance:
                min_distance = distance
                closest_detection = detection
        
        return closest_detection

# 全局YOLO检测器实例
yolo_detector = None

def get_yolo_detector() -> YoloDetector:
    """获取全局YOLO检测器实例"""
    global yolo_detector
    if yolo_detector is None:
        yolo_detector = YoloDetector()
    return yolo_detector
