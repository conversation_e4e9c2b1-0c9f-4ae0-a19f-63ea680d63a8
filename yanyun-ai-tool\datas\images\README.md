# 图像资源目录

此目录用于存放图像识别所需的模板图像和资源文件。

## 目录结构

```
images/
├── ui/                 # UI界面元素
│   ├── buttons/        # 按钮图像
│   ├── menus/          # 菜单界面
│   ├── dialogs/        # 对话框
│   └── icons/          # 图标
├── game_objects/       # 游戏对象
│   ├── enemies/        # 敌人图像
│   ├── npcs/           # NPC图像
│   ├── items/          # 物品图像
│   └── chests/         # 宝箱图像
├── status/             # 状态指示器
│   ├── health_bars/    # 血条
│   ├── mana_bars/      # 蓝条
│   └── buffs/          # 增益效果
└── templates/          # 模板匹配图像
    ├── battle/         # 战斗相关
    ├── quest/          # 任务相关
    └── collection/     # 采集相关
```

## 图像要求

### 格式要求
- 支持格式：PNG, JPG, JPEG
- 推荐使用PNG格式（支持透明度）
- 图像尺寸：建议不超过200x200像素

### 质量要求
- 图像清晰，无模糊
- 颜色准确，对比度适中
- 避免包含过多背景信息
- 确保图像代表性强

### 命名规范
- 使用英文命名，避免中文
- 使用下划线分隔单词
- 包含描述性信息
- 例如：`enemy_boss_dragon.png`

## 模板匹配说明

### UI元素模板
用于识别游戏界面中的各种UI元素：
- 按钮（确认、取消、关闭等）
- 菜单（主菜单、背包、技能等）
- 对话框（任务对话、商店等）
- 状态栏（血条、蓝条、经验条等）

### 游戏对象模板
用于识别游戏世界中的各种对象：
- 敌人（不同类型的怪物）
- NPC（商人、任务发布者等）
- 物品（装备、消耗品等）
- 环境对象（宝箱、传送门等）

### 战斗相关模板
用于战斗系统的图像识别：
- 技能图标
- 目标标记
- 伤害数字
- 战斗状态指示器

## 使用方法

1. **添加新模板**
   - 在游戏中截取目标图像
   - 裁剪出关键部分
   - 保存到对应目录
   - 使用描述性文件名

2. **测试模板**
   - 在不同游戏场景中测试
   - 调整匹配阈值
   - 确保识别准确性

3. **优化模板**
   - 移除冗余背景
   - 调整图像大小
   - 提高对比度

## 注意事项

- 定期更新模板以适应游戏更新
- 备份重要的模板文件
- 测试不同分辨率下的兼容性
- 避免使用受版权保护的游戏资源
