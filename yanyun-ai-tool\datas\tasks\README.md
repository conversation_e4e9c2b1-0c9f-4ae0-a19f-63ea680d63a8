# 任务脚本目录

此目录用于存放自定义任务脚本和预设任务模板。

## 脚本类型

### Python脚本
- 文件扩展名：`.py`
- 支持完整的Python语法
- 可以调用所有AI工具的功能模块

### JSON配置脚本
- 文件扩展名：`.json`
- 声明式配置，易于编辑
- 适合简单的任务流程

## 脚本结构

### Python脚本模板

```python
# -*- coding: utf-8 -*-
"""
自定义任务脚本模板
"""

import time
from yanyunauto.core.game_controller import YanyunGameController
from yanyunauto.utils.logger import logger

class CustomTask:
    def __init__(self, controller):
        self.controller = controller
        self.running = False
    
    def start(self):
        """开始执行任务"""
        self.running = True
        logger.info("开始执行自定义任务")
        
        while self.running:
            try:
                # 在这里编写你的任务逻辑
                self.execute_step()
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"任务执行错误: {e}")
                break
    
    def stop(self):
        """停止任务"""
        self.running = False
        logger.info("任务已停止")
    
    def execute_step(self):
        """执行单步任务"""
        # 示例：检测敌人并攻击
        screenshot = self.controller.capture_screenshot()
        if screenshot is not None:
            detections = self.controller.detect_objects(screenshot)
            enemies = [d for d in detections if d['label'] == '敌人']
            
            if enemies:
                # 攻击最近的敌人
                closest_enemy = min(enemies, key=lambda e: e['distance'])
                self.controller.attack_target(closest_enemy)

# 任务入口点
def create_task(controller):
    return CustomTask(controller)
```

### JSON配置脚本模板

```json
{
    "name": "自定义采集任务",
    "description": "自动采集指定物品",
    "version": "1.0.0",
    "config": {
        "target_items": ["草药", "矿石", "宝箱"],
        "collection_radius": 100,
        "max_duration": 3600,
        "auto_move": true
    },
    "steps": [
        {
            "type": "detection",
            "targets": ["草药", "矿石", "宝箱"],
            "action": "collect"
        },
        {
            "type": "movement",
            "pattern": "random_walk",
            "duration": 5
        },
        {
            "type": "condition",
            "check": "inventory_full",
            "action": "return_to_base"
        }
    ]
}
```

## 预设任务

### 战斗任务
- `auto_battle_basic.py` - 基础自动战斗
- `auto_battle_advanced.py` - 高级战斗策略
- `boss_farming.py` - BOSS刷取

### 采集任务
- `resource_collection.py` - 资源采集
- `treasure_hunting.py` - 宝箱搜寻
- `herb_gathering.py` - 草药采集

### 任务执行
- `quest_automation.py` - 任务自动化
- `daily_quests.py` - 日常任务
- `story_progression.py` - 主线推进

### 探索任务
- `map_exploration.py` - 地图探索
- `secret_finding.py` - 秘密发现
- `achievement_hunting.py` - 成就获取

## 脚本开发指南

### 1. 基础结构
每个Python脚本都应该包含：
- 任务类定义
- `start()` 和 `stop()` 方法
- `create_task()` 工厂函数

### 2. 可用API
脚本可以使用以下API：

```python
# 游戏控制
controller.capture_screenshot()
controller.detect_objects(image)
controller.move_to(x, y)
controller.click_at(x, y)
controller.press_key(key)

# 战斗系统
controller.attack_target(target)
controller.use_skill(skill_id)
controller.dodge()

# 任务系统
controller.accept_quest(quest_id)
controller.submit_quest(quest_id)
controller.get_quest_status()

# 采集系统
controller.collect_item(item)
controller.open_chest(chest)
```

### 3. 错误处理
```python
try:
    # 任务逻辑
    pass
except Exception as e:
    logger.error(f"任务执行失败: {e}")
    # 错误恢复逻辑
```

### 4. 状态管理
```python
# 检查游戏状态
if controller.is_in_battle():
    # 战斗逻辑
elif controller.is_in_dialogue():
    # 对话处理
```

## 调试和测试

### 1. 日志输出
```python
logger.info("任务开始")
logger.debug("检测到敌人")
logger.warning("血量不足")
logger.error("任务失败")
```

### 2. 断点调试
在开发环境中可以使用断点调试：
```python
import pdb; pdb.set_trace()
```

### 3. 性能监控
```python
import time
start_time = time.time()
# 执行任务
execution_time = time.time() - start_time
logger.info(f"任务执行时间: {execution_time:.2f}秒")
```

## 最佳实践

1. **模块化设计** - 将复杂任务分解为小的功能模块
2. **错误恢复** - 实现适当的错误处理和恢复机制
3. **性能优化** - 避免过于频繁的检测和操作
4. **用户友好** - 提供清晰的日志和进度反馈
5. **安全考虑** - 避免可能导致账号风险的操作

## 注意事项

- 脚本执行前请备份游戏存档
- 测试脚本时建议使用测试账号
- 遵守游戏服务条款和使用协议
- 定期更新脚本以适应游戏更新
